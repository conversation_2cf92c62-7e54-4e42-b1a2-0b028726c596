#include "board_config.h"
#include "drv_flash.h"


#if defined(APP_DEBUG)
#include "hal_os.h"
#include "battery_dev.h"
#include "type.h"


static unsigned short cell_voltage[32];
static int sys_current;
static short temperature[11];



unsigned char soft_warn1[8];
unsigned char soft_warn2[8];
unsigned char soft_warn3[8];
unsigned char soft_warn4[8];	
unsigned char soft_warn5[8];

unsigned int tick;

unsigned short sig_r_val;
unsigned char read_mos[3];

unsigned short cells_amounts;
unsigned char temper_amounts;

unsigned char dcmos_status[2];

unsigned char warn_status[6];

static void test_task_entry(void)
{
    (void)get_signals_value(SID_CELLS_AMOUNT, &cells_amounts, 1);
    (void)get_signals_value(SID_CELL1_VOLTAGE, cell_voltage, cells_amounts);
    (void)get_signals_value(SID_CURRENT_BATTERY, &sys_current, 1);
    (void)get_signals_value(SID_CELL_TEMPER_AMOUNT, &temper_amounts, 1);
    (void)get_signals_value(SID_CELL1_TEMPERATURE, temperature, temper_amounts);
    (void)get_signals_value(SID_DMOS_TEMPERATURE, &temperature[4], 1);
	(void)get_signals_value(SID_SAMPLING_R, &sig_r_val, 1);
	(void)get_signals_value(SID_CMOS_STATUS, dcmos_status, 2);
	(void)set_signals_value(SID_CMOS_STATUS, dcmos_status, 2);
	
	
	tick = get_sys_tick();
}
 


typedef struct  {
    unsigned short id_to_get;
    union 
    {
        unsigned char u8_value;
        signed char i8_value;
        unsigned short u16_value;
        signed short i16_value;
        unsigned int u32_value;
        signed int i32_value;
        unsigned long long u64_value;
        long long i64_value;
        char buf[65];
    }value;
}SIG_ID_VALUE_GET_t;

static SIG_ID_VALUE_GET_t s_sig_id_value_get;
static void test_get_sig(void)
{
    static unsigned short s_id_save = 0;
    if (s_sig_id_value_get.id_to_get != s_id_save)
    {
        s_id_save = s_sig_id_value_get.id_to_get;
        s_sig_id_value_get.value.u32_value = 0;
    }
    (void)get_signals_value(s_sig_id_value_get.id_to_get, s_sig_id_value_get.value.buf,1);

}

typedef struct  {
    unsigned short id_to_set;
    union 
    {
        unsigned char u8_value;
        signed char i8_value;
        unsigned short u16_value;
        signed short i16_value;
        unsigned int u32_value;
        signed int i32_value;
        unsigned long long u64_value;
        long long i64_value;
        char buf[65];
    }value;
    unsigned char do_action;
    signed char op_ret;
}SIG_ID_VALUE_SET_t;

static SIG_ID_VALUE_SET_t s_sig_id_value_set;
static void test_set_sig(void)
{
    if (s_sig_id_value_set.do_action != 0)
    {
        s_sig_id_value_set.op_ret=(signed char)set_signals_value(s_sig_id_value_set.id_to_set, &s_sig_id_value_set.value.u32_value,1);
        s_sig_id_value_set.do_action = 0;
    }
}
unsigned char mos_status[3];
static void test_entry(void)
{
    test_get_sig();
    test_set_sig();
    test_task_entry();
    (void)get_signals_value(SID_PRE_DMOS_STATUS, mos_status,3);
}
APP_TASK_DEFINE(test_entry,"3");

SettingPara_t *g_pSettingPara = (SettingPara_t *)SETTING_PARA_SAVE_RAM_ADDR;
static int test_init(void)
{
	char *sid_dtu_ds = "V1.0.0";
	(void)set_signals_value(SID_PRODUCTION_VERSION, sid_dtu_ds, 1);	
	
    return APP_OK;
}

APP_INIT_DEFINE(test_init,"99");


#endif

