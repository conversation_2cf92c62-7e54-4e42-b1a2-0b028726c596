

#include "gd32f30x.h"
#include "board_config.h"
#include "board.h"
#include "hal_os.h"
#include "type.h"
#include <stdio.h>

//================================================ else
void NMI_Handler(void)
{
    APP_PRINTF("NMI INT accur!\r\n");
    NVIC_SystemReset();
}
void HardFault_Handler(void)
{
    APP_PRINTF("HardFault INT accur!\r\n");
    NVIC_SystemReset();
}
void BusFault_Handler(void)
{
    APP_PRINTF("BusFault INT accur!\r\n");
   NVIC_SystemReset();
}
void UsageFault_Handler(void)
{
    APP_PRINTF("UsageFault INT accur!\r\n");
  NVIC_SystemReset();
}

void Default_Handler(void)
{
    APP_PRINTF("Unexpected INT accur!\r\n");
   NVIC_SystemReset();
}
 
void MemManage_Handler(void)
{
    APP_PRINTF("Unexpected INT accur!\r\n");
  NVIC_SystemReset();
}
 
void SVC_Handler(void)
{
	APP_PRINTF("Unexpected INT accur!\r\n");
	NVIC_SystemReset();
}

void DebugMon_Handler(void)
{
	APP_PRINTF("Unexpected INT accur!\r\n");
	NVIC_SystemReset();
}

void PendSV_Handler(void)
{
	APP_PRINTF("Unexpected INT accur!\r\n");
	NVIC_SystemReset();
}

//================================================ NVIC_SystemReset
void mcu_do_reset(void)
{
    NVIC_SystemReset();
}

//================================================ irq_lock
unsigned int irq_lock(void)
{
    unsigned int key;
    key = __get_PRIMASK();
    __disable_irq();
    return key;
}
void irq_unlock(unsigned int key)
{
    __set_PRIMASK(key);
}
 
//================================================ sys_tick
volatile static uint32_t delay;
volatile static unsigned int count_tick;

extern void systick_config(void)
{
    /* setup systick timer for 1000Hz interrupts */
    if (SysTick_Config(24000000 / 1000U)){
        /* capture error */
        while (1){
        }
    }
    /* configure the systick handler priority */
    NVIC_SetPriority(SysTick_IRQn, 0x00U);
}
 

void delay_us(unsigned int time_us)
{
    unsigned int last = SysTick->VAL;
    unsigned int delta = time_us*SysTick->LOAD/(1000);
	/* Wait until the given number of time have passed */
	while ((last - SysTick->VAL) < delta) {
        ;
	}
}
void delay_1ms(unsigned int count)
{
    delay = count;

    while(0U != delay){
    }
}

void delay_decrement(void)
{
    if (0U != delay){
        delay--;
    }
}

void SysTick_Handler(void)
{
	count_tick++;
	delay_decrement();
}

extern unsigned int get_sys_tick(void)
{
	return count_tick;
}
//----------------------------------------------


//void RTC_Alarm_IRQHandler(void)
//{
//	uint32_t TmpTime=0;
//   if(RESET != rtc_flag_get(RTC_FLAG_ALARM))
//	{
// 	   exti_flag_clear(EXTI_17);
//     //rtc_flag_clear(RTC_FLAG_ALARM);
////		flag_wake_type = 1;
//    } 

//}

