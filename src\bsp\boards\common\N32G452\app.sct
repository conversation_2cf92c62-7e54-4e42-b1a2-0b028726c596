
#if (APP_ADDR_FLASH_BEGIN == APP_ADDR_BK_FLASH_BEGIN)
IROM (APP_ADDR_FLASH_BEGIN+APP_BIN_HEADER)  (SIZES_MAX_APP_FLASH-APP_BIN_HEADER-4) {
#else
IROM APP_ADDR_FLASH_BEGIN  (SIZES_MAX_APP_FLASH-APP_BIN_HEADER-4) {
#endif 	
  APP_IROM +0   { 
   *.o (RESET, +First)
   *(InRoot$$Sections)
   .ANY (+RO)
  }
  INIT_IROM AlignExpr(+0,4) SORTTYPE Lexical {
   app.o (.app_init_start +FIRST)
   *.o (.app_init.*)
  }
  INIT_IROM_END +0 {
   app.o (.app_init_end +FIRST)
  }
  TASK_IROM AlignExpr(+0,4) SORTTYPE Lexical {
   app.o (.app_task_start +FIRST)
   *.o (.app_task.*)
  }
  TASK_IROM_END +0 {
   app.o (.app_task_end +FIRST)
  }
  RW_IRAM1 (ADDR_IRAM_BEGIN+SIZES_VTOR+SIZES_IRAM_RESERVE) (SIZES_MAX_IRAM-SIZES_VTOR-SIZES_IRAM_RESERVE)   {  
   * (+RW +ZI)
  }
}

// MODULES_IROM AlignExpr(ImageLimit(APP_IROM)+ImageLength(RW_IRAM1),8) {
//AlignExpr(ImageLimit(APP_IROM)+ImageLength(RW_IRAM1), 8)
