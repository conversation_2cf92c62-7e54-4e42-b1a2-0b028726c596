#include "hal_os.h"
#include "n32g45x.h"
#include "n32g45x_iwdg.h"
#include "board_config.h"


#ifdef SYS_IWDOG_ENABLE

__IO uint32_t TimingDelay = 0;
__IO uint32_t LsiFreq     = 40000;
extern __IO uint16_t CaptureNumber;

__IO uint16_t IC1ReadValue1 = 0, IC1ReadValue2 = 0;
__IO uint16_t CaptureNumber = 0;
__IO uint32_t Capture       = 0;
 
void TIM5_IRQHandler(void)
{
    if (TIM_GetIntStatus(TIM5, TIM_INT_CC4) != RESET)
    {
        if (CaptureNumber == 0)
        {
            /* Get the Input Capture value */
            IC1ReadValue1 = TIM_GetCap4(TIM5);
        }
        else if (CaptureNumber == 2)
        {
            RCC_ClocksType clks;
            /* Get the Input Capture value */
            IC1ReadValue2 = TIM_GetCap4(TIM5);

            /* Capture computation */
            if (IC1ReadValue2 > IC1ReadValue1)
            {
                Capture = (IC1ReadValue2 - IC1ReadValue1);
            }
            else
            {
                Capture = ((0xFFFF - IC1ReadValue1) + IC1ReadValue2);
            }
            RCC_GetClocksFreqValue(&clks);
            /* Frequency computation */
            LsiFreq = (uint32_t)clks.Pclk1Freq / Capture;
            LsiFreq *= 32;
        }

        CaptureNumber++;

        /* Clear TIM5 Capture compare interrupt pending bit */
        TIM_ClrIntPendingBit(TIM5, TIM_INT_CC4);
    }
}

void TIM5_ConfigForLSI(void)
{
    NVIC_InitType NVIC_InitStructure;
    TIM_ICInitType TIM_ICInitStructure;

    /* Enable TIM5 clocks */
    RCC_EnableAPB1PeriphClk(RCC_APB1_PERIPH_TIM5, ENABLE);
    RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);

    /* Enable the TIM5 Interrupt */
    NVIC_InitStructure.NVIC_IRQChannel                   = TIM5_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority        = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd                = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

    /* Configure TIM5 prescaler */
    TIM_ConfigPrescaler(TIM5, 0, TIM_PSC_RELOAD_MODE_IMMEDIATE);

    /* Connect internally the TM5_CH4 Input Capture to the LSI clock output */
    GPIO_ConfigPinRemap(GPIO_RMP_TIM5CH4, ENABLE);

    /* TIM5 configuration: Input Capture mode ---------------------
       The LSI oscillator is connected to TIM5 CH4
       The Rising edge is used as active edge,
       The TIM5 CCDAT4 is used to compute the frequency value
    ------------------------------------------------------------ */
    TIM_ICInitStructure.Channel     = TIM_CH_4;
    TIM_ICInitStructure.IcPolarity  = TIM_IC_POLARITY_RISING;
    TIM_ICInitStructure.IcSelection = TIM_IC_SELECTION_DIRECTTI;
    TIM_ICInitStructure.IcPrescaler = TIM_IC_PSC_DIV8;
    TIM_ICInitStructure.IcFilter    = 0;
    TIM_ICInit(TIM5, &TIM_ICInitStructure);

    /* TIM10 Counter Enable */
    TIM_Enable(TIM5, ENABLE);

    /* Reset the flags */
    TIM5->STS = 0;

    /* Enable the CC4 Interrupt Request */
    TIM_ConfigInt(TIM5, TIM_INT_CC4, ENABLE);
}




int iwdog_init(void)
{
	
	TIM5_ConfigForLSI();
/* Enable write access to IWDG_PR and IWDG_RLR registers */
    IWDG_WriteConfig(IWDG_WRITE_ENABLE);

    /* IWDG counter clock: LSI/32 */
    IWDG_SetPrescalerDiv(IWDG_PRESCALER_DIV128);

    /* Set counter reload value to obtain 250ms IWDG TimeOut.
       Counter Reload Value = 250ms/IWDG counter clock period
                            = 250ms / (LSI/32)
                            = 0.25s / (LsiFreq/32)
                            = LsiFreq/(32 * 4)
                            = LsiFreq/128
     */

    IWDG_CntReload(LsiFreq /2);//2w->11.6s
    /* Reload IWDG counter */
    IWDG_ReloadKey();

    /* Enable IWDG (the LSI oscillator will be enabled by hardware) */
    IWDG_Enable();
	return 0;
}

void feed_iwdog_task(void)
{
	unsigned int current_tick = 0;
	static unsigned int current_tick_save = 0;
	current_tick = get_sys_tick();
	
	if(current_tick - current_tick_save > 1000*1)
	{
		current_tick_save = current_tick;
	 
		IWDG_ReloadKey();
	}
	current_tick = current_tick;
}
 
APP_INIT_DEFINE(iwdog_init,"10");
APP_TASK_DEFINE(feed_iwdog_task,"10");
 

#endif