PROG = 7zDec
CXX = gcc
LIB =
RM = rm -f
CFLAGS = -c -O2 -Wall

OBJS = 7zMain.o 7zAlloc.o 7zArcIn.o 7zBuf.o 7zBuf2.o 7zCrc.o 7zCrcOpt.o 7zDec.o CpuArch.o Delta.o LzmaDec.o Lzma2Dec.o Bra.o Bra86.o BraIA64.o Bcj2.o Ppmd7.o Ppmd7Dec.o 7zFile.o 7zStream.o

all: $(PROG)

$(PROG): $(OBJS)
	$(CXX) -o $(PROG) $(LDFLAGS) $(OBJS) $(LIB)

7zMain.o: 7zMain.c
	$(CXX) $(CFLAGS) 7zMain.c

7zAlloc.o: ../../7zAlloc.c
	$(CXX) $(CFLAGS) ../../7zAlloc.c

7zArcIn.o: ../../7zArcIn.c
	$(CXX) $(CFLAGS) ../../7zArcIn.c

7zBuf.o: ../../7zBuf.c
	$(CXX) $(CFLAGS) ../../7zBuf.c

7zBuf2.o: ../../7zBuf2.c
	$(CXX) $(CFLAGS) ../../7zBuf2.c

7zCrc.o: ../../7zCrc.c
	$(CXX) $(CFLAGS) ../../7zCrc.c

7zCrcOpt.o: ../../7zCrc.c
	$(CXX) $(CFLAGS) ../../7zCrcOpt.c

7zDec.o: ../../7zDec.c
	$(CXX) $(CFLAGS) -D_7ZIP_PPMD_SUPPPORT ../../7zDec.c

CpuArch.o: ../../CpuArch.c
	$(CXX) $(CFLAGS) ../../CpuArch.c

Delta.o: ../../Delta.c
	$(CXX) $(CFLAGS) ../../Delta.c

LzmaDec.o: ../../LzmaDec.c
	$(CXX) $(CFLAGS) ../../LzmaDec.c

Lzma2Dec.o: ../../Lzma2Dec.c
	$(CXX) $(CFLAGS) ../../Lzma2Dec.c

Bra.o: ../../Bra.c
	$(CXX) $(CFLAGS) ../../Bra.c

Bra86.o: ../../Bra86.c
	$(CXX) $(CFLAGS) ../../Bra86.c

BraIA64.o: ../../BraIA64.c
	$(CXX) $(CFLAGS) ../../BraIA64.c

Bcj2.o: ../../Bcj2.c
	$(CXX) $(CFLAGS) ../../Bcj2.c

Ppmd7.o: ../../Ppmd7.c
	$(CXX) $(CFLAGS) ../../Ppmd7.c

Ppmd7Dec.o: ../../Ppmd7Dec.c
	$(CXX) $(CFLAGS) ../../Ppmd7Dec.c

7zFile.o: ../../7zFile.c
	$(CXX) $(CFLAGS) ../../7zFile.c

7zStream.o: ../../7zStream.c
	$(CXX) $(CFLAGS) ../../7zStream.c

clean:
	-$(RM) $(PROG) $(OBJS)
