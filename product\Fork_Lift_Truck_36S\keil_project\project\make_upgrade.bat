@echo off
setlocal EnableDelayedExpansion

::echo Step 1: Setting up input file path...
set "INPUT_FILE=..\..\src\production_info.h"
::echo Input file path: %INPUT_FILE%
::pause

::echo.
echo Step 2: Initializing variables...
set "SOFTWARE_VERSION="
set "HARDWARE_VERSION="
::echo Variables initialized
::pause

::echo.
::echo Step 3: Reading file and extracting version information...
for /f "tokens=2,3" %%a in ('findstr "PRODUCTION_SVNUMB PRODUCTION_HVNUMB" "%INPUT_FILE%"') do (
    set "define=%%a"
    set "value=%%b"
    :: 移除引号
    set "value=!value:"=!"
    
    if "!define!"=="PRODUCTION_SVNUMB" (
        set "SOFTWARE_VERSION=!value!"
        echo Found SOFTWARE_VERSION: !value!
    )
    if "!define!"=="PRODUCTION_HVNUMB" (
        set "HARDWARE_VERSION=!value!"
        echo Found HARDWARE_VERSION: !value!
    )
)
::pause

::echo.
::echo Step 4: Checking extracted values...
if "%SOFTWARE_VERSION%"=="" (
    echo Error: Failed to extract SOFTWARE_VERSION
    pause
    exit /b 1
)
if "%HARDWARE_VERSION%"=="" (
    echo Error: Failed to extract HARDWARE_VERSION
    pause
    exit /b 1
)

::echo.
::echo Step 5: Final Results:
::echo SOFTWARE_VERSION = %SOFTWARE_VERSION%
::echo HARDWARE_VERSION = %HARDWARE_VERSION%
::pause


::set SOFTWARE_VERSION=%1
::set HARDWARE_VERSION=%2
set PRODUCT_NAME
set SOFTWARE_MAGIC_NUMB=0x2e4f544b49452e2e


C:\Keil_v5\ARM\ARMCC\bin\fromelf.exe --bin -o  firmware\app.bin   Objects_app\OUTPUT\app.axf

copy  Objects_app\OUTPUT\app.hex   firmware\
copy  Objects_boot\OUTPUT\boot.hex   firmware\

..\..\..\..\open_src\lzma1900\bin\lzma.exe e firmware\app.bin firmware\lzma_app.bin -a1 -d13

..\..\..\..\tools\make_load\make_load.exe  ..\..\release\%PRODUCT_NAME%%SOFTWARE_VERSION%.bin          firmware\lzma_app.bin     %SOFTWARE_MAGIC_NUMB% "%PRODUCT_NAME%%SOFTWARE_VERSION%" 0x22 0x11 0x11 "%HARDWARE_VERSION%"
..\..\..\..\tools\hex_all\hex_all.exe                ..\..\release\%PRODUCT_NAME%%SOFTWARE_VERSION%.hex        firmware\boot.hex                  firmware\app.hex  

..\..\..\..\tools\buildBatterySrc.vbs Fork_Lift_Truck_36S Fork_Lift_Truck_36S_



echo.
echo Process completed successfully
::pause
exit /b 0