
#define MAX_VAL 150
#define MIN_VAL 200

unsigned short get_full_charge_capacity(void)
{
	unsigned short full_chg_capacity = 0;
	(void)get_signals_value(SID_BATT_FULL_CHARGE_CAPACITY, &full_chg_capacity, 1);	
	return full_chg_capacity;	
	
	//温度控制满充容量
	#if 0
	short temper_min;
	unsigned short full_chg_capacity = 0;
	(void)get_signals_value(SID_CELL_TEMPER_MIN, &temper_min, 1);
	(void)get_signals_value(SID_BATT_FULL_CHARGE_CAPACITY, &full_chg_capacity, 1);	
	if(temper_min > MAX_VAL)
	{
		(void)set_signals_value(SID_FULL_CAP,&full_chg_capacity, 1);
		return full_chg_capacity;
	}
	
	short temper_max;
	short temper_thr;
	(void)get_signals_value(SID_CELL_TEMPER_MAX, &temper_max, 1);
	(void)get_signals_value(SID_TEMPER_WIRE_BROKEN_RAISE_THR, &temper_thr, 1);
	if((temper_min == TEMPER_INVALID_VALUE)||(temper_max-temper_min>temper_thr))
	{
		(void)set_signals_value(SID_FULL_CAP,&full_chg_capacity, 1);
		return full_chg_capacity;
	}

    int T_input = 0;
    int temp_min = 0;
    short temp_max = 0;	
    short soh = 0;
	unsigned int temp_full_chg_capacity = 0;
	
	T_input = temper_min;	
	if(T_input<-MIN_VAL)
		T_input = -MIN_VAL;
	else if(T_input>MAX_VAL)
		T_input = MAX_VAL;

	temp_min = ((T_input+MIN_VAL)*100);
	temp_max = (MAX_VAL+MIN_VAL);
	T_input = (temp_min/temp_max);
	soh = 700 + T_input*3;//0.1%soh
	if(soh>1000)
		soh	= 1000;
	temp_full_chg_capacity = (full_chg_capacity*soh)/1000;
	
	if(temp_full_chg_capacity<20000)
		temp_full_chg_capacity=20000;
	else if(temp_full_chg_capacity>30000)
		temp_full_chg_capacity=30000;
		
	(void)set_signals_value(SID_FULL_CAP,&temp_full_chg_capacity, 1);	
	return temp_full_chg_capacity;	
	#endif
}