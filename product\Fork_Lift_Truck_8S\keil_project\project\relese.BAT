SET PATH=C:\Keil_v5\ARM\ARMCC\Bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Microsoft VS Code\bin;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files (x86)\Git\cmd;C:\Program Files\TortoiseGit\bin;C:\Strawberry\c\bin;C:\Strawberry\perl\site\bin;C:\Strawberry\perl\bin;C:\Program Files\MATLAB\R2022b\runtime\win64;C:\Program Files\MATLAB\R2022b\bin;C:\Users\<USER>\AppData\Local\ActiveState\StateTool\release\bin;C:\Users\<USER>\AppData\Local\activestate\cache\bin;y\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\MentorGraphics\PADSVX.2.7\SDD_HOME\CAMCAD;C:\Users\<USER>\.dotnet\tools;C:\MinGW\bin;;C:\Users\<USER>\AppData\Roaming\yt_config_tool\ninja-win;C:\Users\<USER>\AppData\Roaming\yt_config_tool\gcc-arm-none-eabi-10.3-2021.10\bin;C:\Users\<USER>\AppData\Roaming\yt_config_tool\cmake-3.26.4-windows-x86_64\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
SET CPU_TYPE=N32G452VEL7
SET CPU_VENDOR=Nationstech
SET UV2_TARGET=relese
SET CPU_CLOCK=0x00B71B00
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\app.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\app_debug.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\program_common.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\battery_dev.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\afe_ms9930.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\balance.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\sox.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\voltage_temperature_arbitrate.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\math.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\vehicle_protocol.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\adc_n32g45x.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\crc.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\product.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\control_logic.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\charger_protocol.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\charger.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\tools.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\hw_agus_app.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\heater_ctrl.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\led_ctrl_simply.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\sop.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\full_charge_correction.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\buzzer_ctrl.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\key.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\board.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmAsm" --Via ".\objects_app\output\startup_n32g45x._ia"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\system_n32g45x.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\bsp_flash.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\eikto_canbus.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\eikto_protocol.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\upgrade_common.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\bsp_delay.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_it.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\bsp_gpio.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\bsp_i2c.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\can_n32g4.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\drv_ring_queue.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\bsp_n32g45x_uart.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\misc.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_adc.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_bkp.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_can.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_comp.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_crc.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_dac.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_dbg.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_dma.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_dvp.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_eth.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_exti.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_flash.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_gpio.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_i2c.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_iwdg.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_opamp.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_pwr.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_qspi.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_rcc.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_rtc.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_sdio.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_spi.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_tim.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_tsc.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_usart.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\n32g45x_wwdg.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\hw_agus_event.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\hw_agus_heap.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\hw_agus_mem.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\hw_agus_utils.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\hw_ec200_communication .__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\hw_ec200_driver.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects_app\output\hw_ring_buffer.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmLink" --Via ".\Objects_app\OUTPUT\app.lnp"
"C:\Keil_v5\ARM\ARMCC\Bin\fromelf.exe" ".\Objects_app\OUTPUT\app.axf" --i32combined --output ".\Objects_app\OUTPUT\app.hex"
fromelf --bin -o "D:\RD86_new_data_cfg_1\BMS_MASTER\product\Fork_Lift_Truck_8S\keil_project\project\Objects_app\OUTPUT\app.bin" "D:\RD86_new_data_cfg_1\BMS_MASTER\product\Fork_Lift_Truck_8S\keil_project\project\Objects_app\OUTPUT\app.axf
make_upgrade.bat  V1.0.01
