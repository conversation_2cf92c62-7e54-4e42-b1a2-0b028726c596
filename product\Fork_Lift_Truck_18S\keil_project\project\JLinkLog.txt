T2228 8896:894.976   SEGGER J-Link V7.96j Log File
T2228 8896:894.976   DLL Compiled: May 29 2024 15:36:36
T2228 8896:894.976   Logging started @ 2025-08-01 08:42
T2228 8896:894.976   Process: C:\Keil_v5\UV4\UV4.EXE
T2228 8896:894.976 - 45531.336ms
T2228 8896:894.976 JLINK_<PERSON><PERSON>arn<PERSON><PERSON><PERSON><PERSON><PERSON>(...)
T2228 8896:894.976 - 0.004ms
T2228 8896:894.976 JLINK_OpenEx(...)
T2228 8896:898.048   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T2228 8896:899.072   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T2228 8896:899.072   Decompressing FW timestamp took 118 us
T2228 8896:907.264   Hardware: V9.40
T2228 8896:907.264   S/N: 69406248
T2228 8896:907.264   OEM: <PERSON><PERSON><PERSON><PERSON>
T2228 8896:907.264   Feature(s): R<PERSON>, GD<PERSON>, <PERSON><PERSON><PERSON>, FlashB<PERSON>, JFlash
T2228 8896:907.264   Bootloader: (Could not read)
T2228 8896:908.288   TELNET listener socket opened on port 19021
T2228 8896:913.408   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
T2228 8896:913.408   WEBSRV Webserver running on local port 19080
T2228 8896:913.408   Looking for J-Link GUI Server exe at: C:\Keil_v5\ARM\Segger\JLinkGUIServer.exe
T2228 8896:913.408   Forking J-Link GUI Server: C:\Keil_v5\ARM\Segger\JLinkGUIServer.exe
T2228 8896:942.080   J-Link GUI Server info: "J-Link GUI server V7.96j "
T2228 8896:948.224 - 52.464ms returns "O.K."
T2228 8896:948.224 JLINK_GetEmuCaps()
T2228 8896:948.224 - 0.005ms returns 0xB9FF7BBF
T2228 8896:948.224 JLINK_TIF_GetAvailable(...)
T2228 8896:948.224 - 0.261ms
T2228 8896:948.224 JLINK_SetErrorOutHandler(...)
T2228 8896:948.224 - 0.015ms
T2228 8896:948.224 JLINK_ExecCommand("ProjectFile = "D:\work\bms\mix3\BMS_MASTER\product\Fork_Lift_Truck_18S\keil_project\project\JLinkSettings.ini"", ...). 
T2228 8896:951.296 - 2.723ms returns 0x00
T2228 8896:951.296 JLINK_ExecCommand("Device = ARMCM4_FP", ...). 
T2228 8896:951.296   Device "CORTEX-M4" selected.
T2228 8896:952.320 - 0.380ms returns 0x00
T2228 8896:952.320 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
T2228 8896:952.320   ERROR: Unknown command
T2228 8896:952.320 - 0.008ms returns 0x01
T2228 8896:952.320 JLINK_GetHardwareVersion()
T2228 8896:952.320 - 0.004ms returns 94000
T2228 8896:952.320 JLINK_GetDLLVersion()
T2228 8896:952.320 - 0.004ms returns 79610
T2228 8896:952.320 JLINK_GetOEMString(...)
T2228 8896:952.320 JLINK_GetFirmwareString(...)
T2228 8896:952.320 - 0.004ms
T2228 8896:952.320 JLINK_GetDLLVersion()
T2228 8896:952.320 - 0.004ms returns 79610
T2228 8896:952.320 JLINK_GetCompileDateTime()
T2228 8896:952.320 - 0.004ms
T2228 8896:952.320 JLINK_GetFirmwareString(...)
T2228 8896:952.320 - 0.004ms
T2228 8896:952.320 JLINK_GetHardwareVersion()
T2228 8896:952.320 - 0.004ms returns 94000
T2228 8896:952.320 JLINK_GetSN()
T2228 8896:952.320 - 0.004ms returns 69406248
T2228 8896:952.320 JLINK_GetOEMString(...)
T2228 8896:952.320 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T2228 8896:952.320 - 0.789ms returns 0x00
T2228 8896:952.320 JLINK_HasError()
T2228 8896:952.320 JLINK_SetSpeed(10000)
T2228 8896:952.320 - 0.095ms
T2228 8896:952.320 JLINK_GetId()
T2228 8896:953.344   Found SW-DP with ID 0x2BA01477
T2228 8896:956.416   DPIDR: 0x2BA01477
T2228 8896:956.416   CoreSight SoC-400 or earlier
T2228 8896:956.416   Scanning AP map to find all available APs
T2228 8896:956.416   AP[1]: Stopped AP scan as end of AP map has been reached
T2228 8896:956.416   AP[0]: AHB-AP (IDR: 0x24770011)
T2228 8896:956.416   Iterating through AP map to find AHB-AP to use
T2228 8896:957.440   AP[0]: Core found
T2228 8896:957.440   AP[0]: AHB-AP ROM base: 0xE00FF000
T2228 8896:957.440   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T2228 8896:958.464   Found Cortex-M4 r0p1, Little endian.
T2228 8896:958.464   -- Max. mem block: 0x00010E60
T2228 8896:959.488   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2228 8896:959.488   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T2228 8896:959.488   CPU_ReadMem(4 bytes @ 0x********)
T2228 8896:960.512   FPUnit: 6 code (BP) slots and 2 literal slots
T2228 8896:960.512   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T2228 8896:960.512   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T2228 8896:960.512   CPU_ReadMem(4 bytes @ 0x********)
T2228 8896:960.512   CPU_WriteMem(4 bytes @ 0x********)
T2228 8896:960.512   CPU_ReadMem(4 bytes @ 0xE000ED88)
T2228 8896:961.536   CPU_WriteMem(4 bytes @ 0xE000ED88)
T2228 8896:961.536   CPU_ReadMem(4 bytes @ 0xE000ED88)
T2228 8896:962.560   CPU_WriteMem(4 bytes @ 0xE000ED88)
T2228 8896:962.560   CoreSight components:
T2228 8896:962.560   ROMTbl[0] @ E00FF000
T2228 8896:962.560   CPU_ReadMem(64 bytes @ 0xE00FF000)
T2228 8896:963.584   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T2228 8896:964.608   [0][0]: E000E000 CID B105E00D PID 000BB00C SCS-M7
T2228 8896:964.608   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T2228 8896:964.608   [0][1]: ******** CID B105E00D PID 003BB002 DWT
T2228 8896:964.608   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T2228 8896:964.608   [0][2]: ******** CID B105E00D PID 002BB003 FPB
T2228 8896:964.608   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T2228 8896:964.608   [0][3]: ******** CID B105E00D PID 003BB001 ITM
T2228 8896:964.608   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T2228 8896:965.632   [0][4]: ******** CID B105900D PID 000BB9A1 TPIU
T2228 8896:965.632 - 13.471ms returns 0x2BA01477
T2228 8896:965.632 JLINK_GetDLLVersion()
T2228 8896:965.632 - 0.004ms returns 79610
T2228 8896:965.632 JLINK_CORE_GetFound()
T2228 8896:966.656 - 0.005ms returns 0xE0000FF
T2228 8896:966.656 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T2228 8896:966.656   Value=0xE00FF000
T2228 8896:966.656 - 0.012ms returns 0
T2228 8896:966.656 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T2228 8896:966.656   Value=0xE00FF000
T2228 8896:966.656 - 0.010ms returns 0
T2228 8896:966.656 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T2228 8896:966.656   Value=0x00000000
T2228 8896:966.656 - 0.010ms returns 0
T2228 8896:966.656 JLINK_ReadMemEx(0xE0041FF0, 0x10 Bytes, Flags = 0x02000004)
T2228 8896:966.656   CPU_ReadMem(16 bytes @ 0xE0041FF0)
T2228 8896:966.656   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
T2228 8896:966.656 - 0.389ms returns 16 (0x10)
T2228 8896:966.656 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T2228 8896:966.656   Value=0x00000000
T2228 8896:966.656 - 0.012ms returns 0
T2228 8896:966.656 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T2228 8896:966.656   Value=0x********
T2228 8896:966.656 - 0.010ms returns 0
T2228 8896:966.656 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T2228 8896:966.656   Value=0x********
T2228 8896:966.656 - 0.010ms returns 0
T2228 8896:966.656 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T2228 8896:966.656   Value=0x********
T2228 8896:966.656 - 0.010ms returns 0
T2228 8896:966.656 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T2228 8896:966.656   Value=0x********
T2228 8896:966.656 - 0.010ms returns 0
T2228 8896:966.656 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T2228 8896:966.656   Value=0xE000E000
T2228 8896:966.656 - 0.010ms returns 0
T2228 8896:966.656 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T2228 8896:966.656   Value=0xE000EDF0
T2228 8896:966.656 - 0.010ms returns 0
T2228 8896:966.656 JLINK_GetDebugInfo(0x01 = Unknown)
T2228 8896:966.656   Value=0x00000001
T2228 8896:966.656 - 0.010ms returns 0
T2228 8896:966.656 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T2228 8896:966.656   CPU_ReadMem(4 bytes @ 0xE000ED00)
T2228 8896:967.680   Data:  41 C2 0F 41
T2228 8896:967.680   Debug reg: CPUID
T2228 8896:967.680 - 0.288ms returns 1 (0x1)
T2228 8896:967.680 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T2228 8896:967.680   Value=0x00000000
T2228 8896:967.680 - 0.011ms returns 0
T2228 8896:967.680 JLINK_HasError()
T2228 8896:967.680 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T2228 8896:967.680 - 0.005ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T2228 8896:967.680 JLINK_Reset()
T2228 8896:967.680   CPU is running
T2228 8896:967.680   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T2228 8896:967.680   CPU is running
T2228 8896:967.680   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T2228 8896:967.680   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T2228 8896:968.704   Reset: Reset device via AIRCR.SYSRESETREQ.
T2228 8896:968.704   CPU is running
T2228 8896:968.704   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T2228 8897:020.928   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2228 8897:021.952   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2228 8897:025.024   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T2228 8897:031.168   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2228 8897:034.240   CPU_WriteMem(4 bytes @ 0x********)
T2228 8897:034.240   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T2228 8897:034.240   CPU_ReadMem(4 bytes @ 0x********)
T2228 8897:035.264 - 67.890ms
T2228 8897:035.264 JLINK_HasError()
T2228 8897:035.264 JLINK_ReadReg(R15 (PC))
T2228 8897:035.264 - 0.009ms returns 0x00000958
T2228 8897:035.264 JLINK_ReadReg(XPSR)
T2228 8897:035.264 - 0.006ms returns 0x01000000
T2228 8897:035.264 JLINK_Halt()
T2228 8897:035.264 - 0.005ms returns 0x00
T2228 8897:035.264 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T2228 8897:035.264   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2228 8897:035.264   Data:  03 00 03 00
T2228 8897:035.264   Debug reg: DHCSR
T2228 8897:035.264 - 0.319ms returns 1 (0x1)
T2228 8897:035.264 JLINK_WriteU32_64(0xE000EDF0, 0xA05F0003)
T2228 8897:035.264   Debug reg: DHCSR
T2228 8897:035.264   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T2228 8897:036.288 - 0.730ms returns 0 (0x00000000)
T2228 8897:036.288 JLINK_WriteU32_64(0xE000EDFC, 0x01000000)
T2228 8897:036.288   Debug reg: DEMCR
T2228 8897:036.288   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T2228 8897:036.288 - 0.406ms returns 0 (0x00000000)
T2228 8897:037.312 JLINK_GetHWStatus(...)
T2228 8897:037.312 - 0.145ms returns 0
T2228 8897:037.312 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T2228 8897:037.312 - 0.006ms returns 0x06
T2228 8897:037.312 JLINK_GetNumBPUnits(Type = 0xF0)
T2228 8897:037.312 - 0.005ms returns 0x2000
T2228 8897:037.312 JLINK_GetNumWPUnits()
T2228 8897:037.312 - 0.005ms returns 4
T2228 8897:037.312 JLINK_GetSpeed()
T2228 8897:037.312 - 0.005ms returns 6000
T2228 8897:037.312 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T2228 8897:037.312   CPU_ReadMem(4 bytes @ 0xE000E004)
T2228 8897:037.312   Data:  01 00 00 00
T2228 8897:037.312 - 0.312ms returns 1 (0x1)
T2228 8897:037.312 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T2228 8897:037.312   CPU_ReadMem(4 bytes @ 0xE000E004)
T2228 8897:038.336   Data:  01 00 00 00
T2228 8897:038.336 - 0.403ms returns 1 (0x1)
T2228 8897:038.336 JLINK_WriteMemEx(0x********, 0x0000001C Bytes, Flags = 0x02000004)
T2228 8897:038.336   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T2228 8897:038.336   CPU_WriteMem(28 bytes @ 0x********)
T2228 8897:038.336 - 0.510ms returns 0x1C
T2228 8897:038.336 JLINK_HasError()
T2228 8897:038.336 JLINK_ReadReg(R15 (PC))
T2228 8897:038.336 - 0.006ms returns 0x00000958
T2228 8897:038.336 JLINK_ReadReg(XPSR)
T2228 8897:038.336 - 0.005ms returns 0x01000000
T2228 8897:046.528 JLINK_ReadMemEx(0xE0001004, 0x4 Bytes, Flags = 0x02000000)
T2228 8897:046.528   Data:  00 00 00 00
T2228 8897:046.528   Debug reg: DWT_CYCCNT
T2228 8897:046.528 - 0.048ms returns 4 (0x4)
T2228 8897:201.152 JLINK_HasError()
T2228 8897:201.152 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T2228 8897:201.152 - 0.008ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T2228 8897:201.152 JLINK_Reset()
T2228 8897:201.152   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T2228 8897:202.176   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T2228 8897:202.176   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T2228 8897:202.176   Reset: Reset device via AIRCR.SYSRESETREQ.
T2228 8897:202.176   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T2228 8897:255.424   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2228 8897:255.424   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2228 8897:256.448   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T2228 8897:262.592   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2228 8897:264.640   CPU_WriteMem(4 bytes @ 0x********)
T2228 8897:265.664   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T2228 8897:265.664   CPU_ReadMem(4 bytes @ 0x********)
T2228 8897:266.688 - 65.138ms
T2228 8897:266.688 JLINK_HasError()
T2228 8897:266.688 JLINK_ReadReg(R15 (PC))
T2228 8897:266.688 - 0.007ms returns 0x00000958
T2228 8897:266.688 JLINK_ReadReg(XPSR)
T2228 8897:266.688 - 0.004ms returns 0x01000000
T2228 8897:266.688 JLINK_ReadMemEx(0x00000958, 0x3C Bytes, Flags = 0x02000000)
T2228 8897:266.688   CPU_ReadMem(60 bytes @ 0x00000958)
T2228 8897:267.712   Data:  06 48 80 47 06 48 00 47 FE E7 FE E7 FE E7 FE E7 ...
T2228 8897:267.712 - 0.725ms returns 60 (0x3C)
T2228 8899:647.488 JLINK_HasError()
T2228 8899:647.488 JLINK_ReadReg(R0)
T2228 8899:647.488 - 0.346ms returns 0x00000000
T2228 8899:647.488 JLINK_ReadReg(R1)
T2228 8899:647.488 - 0.005ms returns 0x00000000
T2228 8899:647.488 JLINK_ReadReg(R2)
T2228 8899:647.488 - 0.004ms returns 0x00000000
T2228 8899:647.488 JLINK_ReadReg(R3)
T2228 8899:647.488 - 0.004ms returns 0x00000000
T2228 8899:647.488 JLINK_ReadReg(R4)
T2228 8899:647.488 - 0.004ms returns 0x00000000
T2228 8899:647.488 JLINK_ReadReg(R5)
T2228 8899:647.488 - 0.004ms returns 0x00000000
T2228 8899:647.488 JLINK_ReadReg(R6)
T2228 8899:647.488 - 0.004ms returns 0x00000000
T2228 8899:647.488 JLINK_ReadReg(R7)
T2228 8899:647.488 - 0.004ms returns 0x00000000
T2228 8899:647.488 JLINK_ReadReg(R8)
T2228 8899:647.488 - 0.004ms returns 0x00000000
T2228 8899:647.488 JLINK_ReadReg(R9)
T2228 8899:647.488 - 0.004ms returns 0x00000000
T2228 8899:647.488 JLINK_ReadReg(R10)
T2228 8899:647.488 - 0.004ms returns 0x00000000
T2228 8899:647.488 JLINK_ReadReg(R11)
T2228 8899:647.488 - 0.004ms returns 0x00000000
T2228 8899:647.488 JLINK_ReadReg(R12)
T2228 8899:647.488 - 0.004ms returns 0x00000000
T2228 8899:647.488 JLINK_ReadReg(R13 (SP))
T2228 8899:647.488 - 0.004ms returns 0x20011750
T2228 8899:647.488 JLINK_ReadReg(R14)
T2228 8899:647.488 - 0.004ms returns 0xFFFFFFFF
T2228 8899:647.488 JLINK_ReadReg(R15 (PC))
T2228 8899:647.488 - 0.004ms returns 0x00000958
T2228 8899:647.488 JLINK_ReadReg(XPSR)
T2228 8899:647.488 - 0.004ms returns 0x01000000
T2228 8899:647.488 JLINK_ReadReg(MSP)
T2228 8899:647.488 - 0.004ms returns 0x20011750
T2228 8899:647.488 JLINK_ReadReg(PSP)
T2228 8899:647.488 - 0.004ms returns 0x00000000
T2228 8899:647.488 JLINK_ReadReg(CFBP)
T2228 8899:647.488 - 0.004ms returns 0x00000000
T2228 8899:647.488 JLINK_ReadReg(FPSCR)
T2228 8899:651.584 - 4.059ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS0)
T2228 8899:651.584 - 0.008ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS1)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS2)
T2228 8899:651.584 - 0.005ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS3)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS4)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS5)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS6)
T2228 8899:651.584 - 0.045ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS7)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS8)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS9)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS10)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS11)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS12)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS13)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS14)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS15)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS16)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS17)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS18)
T2228 8899:651.584 - 0.007ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS19)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS20)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS21)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS22)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS23)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS24)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS25)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS26)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS27)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS28)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS29)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS30)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadReg(FPS31)
T2228 8899:651.584 - 0.004ms returns 0x00000000
T2228 8899:651.584 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8899:651.584   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8899:651.584   Data:  00 08 00 20
T2228 8899:651.584 - 0.337ms returns 4 (0x4)
T6778 8899:823.616 JLINK_ReadMemEx(0x00000958, 0x2 Bytes, Flags = 0x02000000)
T6778 8899:823.616   CPU_ReadMem(2 bytes @ 0x00000958)
T6778 8899:823.616   Data:  06 48
T6778 8899:823.616 - 0.421ms returns 2 (0x2)
T6778 8899:823.616 JLINK_HasError()
T6778 8899:823.616 JLINK_SetBPEx(Addr = 0x00008308, Type = 0xFFFFFFF2)
T6778 8899:823.616 - 0.008ms returns 0x00000001
T6778 8899:823.616 JLINK_HasError()
T6778 8899:823.616 JLINK_HasError()
T6778 8899:823.616 JLINK_Go()
T6778 8899:823.616   CPU_WriteMem(4 bytes @ 0x********)
T6778 8899:823.616   CPU_ReadMem(4 bytes @ 0x********)
T6778 8899:824.640   CPU_WriteMem(4 bytes @ 0xE0002008)
T6778 8899:824.640   CPU_WriteMem(4 bytes @ 0xE000200C)
T6778 8899:824.640   CPU_WriteMem(4 bytes @ 0xE0002010)
T6778 8899:824.640   CPU_WriteMem(4 bytes @ 0xE0002014)
T6778 8899:824.640   CPU_WriteMem(4 bytes @ 0xE0002018)
T6778 8899:824.640   CPU_WriteMem(4 bytes @ 0xE000201C)
T6778 8899:825.664   CPU_WriteMem(4 bytes @ 0xE0001004)
T6778 8899:827.712   Memory map 'after startup completion point' is active
T6778 8899:827.712 - 3.642ms
T6778 8899:928.064 JLINK_HasError()
T6778 8899:928.064 JLINK_IsHalted()
T6778 8899:931.136 - 2.822ms returns TRUE
T6778 8899:931.136 JLINK_HasError()
T6778 8899:931.136 JLINK_Halt()
T6778 8899:931.136 - 0.005ms returns 0x00
T6778 8899:931.136 JLINK_IsHalted()
T6778 8899:931.136 - 0.005ms returns TRUE
T6778 8899:931.136 JLINK_IsHalted()
T6778 8899:931.136 - 0.005ms returns TRUE
T6778 8899:931.136 JLINK_IsHalted()
T6778 8899:931.136 - 0.005ms returns TRUE
T6778 8899:931.136 JLINK_HasError()
T6778 8899:931.136 JLINK_ReadReg(R15 (PC))
T6778 8899:931.136 - 0.008ms returns 0x00008308
T6778 8899:931.136 JLINK_ReadReg(XPSR)
T6778 8899:931.136 - 0.006ms returns 0x61000000
T6778 8899:931.136 JLINK_HasError()
T6778 8899:931.136 JLINK_ClrBPEx(BPHandle = 0x00000001)
T6778 8899:931.136 - 0.006ms returns 0x00
T6778 8899:931.136 JLINK_HasError()
T6778 8899:931.136 JLINK_HasError()
T6778 8899:931.136 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T6778 8899:931.136   CPU_ReadMem(4 bytes @ 0xE000ED30)
T6778 8899:932.160   Data:  02 00 00 00
T6778 8899:932.160 - 0.482ms returns 1 (0x1)
T6778 8899:932.160 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T6778 8899:932.160   CPU_ReadMem(4 bytes @ 0xE0001028)
T6778 8899:932.160   Data:  00 00 00 00
T6778 8899:932.160   Debug reg: DWT_FUNC[0]
T6778 8899:932.160 - 0.394ms returns 1 (0x1)
T6778 8899:932.160 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T6778 8899:932.160   CPU_ReadMem(4 bytes @ 0xE0001038)
T6778 8899:933.184   Data:  00 02 00 00
T6778 8899:933.184   Debug reg: DWT_FUNC[1]
T6778 8899:933.184 - 0.399ms returns 1 (0x1)
T6778 8899:933.184 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T6778 8899:933.184   CPU_ReadMem(4 bytes @ 0xE0001048)
T6778 8899:933.184   Data:  00 00 00 00
T6778 8899:933.184   Debug reg: DWT_FUNC[2]
T6778 8899:933.184 - 0.422ms returns 1 (0x1)
T6778 8899:933.184 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T6778 8899:933.184   CPU_ReadMem(4 bytes @ 0xE0001058)
T6778 8899:934.208   Data:  00 00 00 00
T6778 8899:934.208   Debug reg: DWT_FUNC[3]
T6778 8899:934.208 - 0.377ms returns 1 (0x1)
T6778 8899:934.208 JLINK_HasError()
T6778 8899:934.208 JLINK_ReadReg(R0)
T6778 8899:934.208 - 0.006ms returns 0x20012D10
T6778 8899:934.208 JLINK_ReadReg(R1)
T6778 8899:934.208 - 0.005ms returns 0x20016D10
T6778 8899:934.208 JLINK_ReadReg(R2)
T6778 8899:934.208 - 0.005ms returns 0x20016D10
T6778 8899:934.208 JLINK_ReadReg(R3)
T6778 8899:934.208 - 0.005ms returns 0x20016D10
T6778 8899:934.208 JLINK_ReadReg(R4)
T6778 8899:934.208 - 0.005ms returns 0x00000000
T6778 8899:934.208 JLINK_ReadReg(R5)
T6778 8899:934.208 - 0.005ms returns 0x20012CB0
T6778 8899:934.208 JLINK_ReadReg(R6)
T6778 8899:934.208 - 0.005ms returns 0x00000000
T6778 8899:934.208 JLINK_ReadReg(R7)
T6778 8899:934.208 - 0.005ms returns 0x00000000
T6778 8899:934.208 JLINK_ReadReg(R8)
T6778 8899:934.208 - 0.005ms returns 0x00000000
T6778 8899:934.208 JLINK_ReadReg(R9)
T6778 8899:934.208 - 0.005ms returns 0x00000000
T6778 8899:934.208 JLINK_ReadReg(R10)
T6778 8899:934.208 - 0.005ms returns 0x0004B698
T6778 8899:934.208 JLINK_ReadReg(R11)
T6778 8899:934.208 - 0.005ms returns 0x00000000
T6778 8899:934.208 JLINK_ReadReg(R12)
T6778 8899:934.208 - 0.005ms returns 0x20012D20
T6778 8899:934.208 JLINK_ReadReg(R13 (SP))
T6778 8899:934.208 - 0.005ms returns 0x2001ED10
T6778 8899:934.208 JLINK_ReadReg(R14)
T6778 8899:934.208 - 0.005ms returns 0x000082F7
T6778 8899:934.208 JLINK_ReadReg(R15 (PC))
T6778 8899:934.208 - 0.005ms returns 0x00008308
T6778 8899:934.208 JLINK_ReadReg(XPSR)
T6778 8899:934.208 - 0.005ms returns 0x61000000
T6778 8899:934.208 JLINK_ReadReg(MSP)
T6778 8899:934.208 - 0.005ms returns 0x2001ED10
T6778 8899:934.208 JLINK_ReadReg(PSP)
T6778 8899:934.208 - 0.005ms returns 0x00000000
T6778 8899:934.208 JLINK_ReadReg(CFBP)
T6778 8899:934.208 - 0.005ms returns 0x04000000
T6778 8899:934.208 JLINK_ReadReg(FPSCR)
T6778 8899:938.304 - 4.085ms returns 0x03000000
T6778 8899:938.304 JLINK_ReadReg(FPS0)
T6778 8899:938.304 - 0.009ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS1)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS2)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS3)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS4)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS5)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS6)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS7)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS8)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS9)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS10)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS11)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS12)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS13)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS14)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS15)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS16)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS17)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS18)
T6778 8899:938.304 - 0.013ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS19)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS20)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS21)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS22)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS23)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS24)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS25)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS26)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS27)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS28)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS29)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS30)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T6778 8899:938.304 JLINK_ReadReg(FPS31)
T6778 8899:938.304 - 0.006ms returns 0x00000000
T2228 8899:945.472 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8899:945.472   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8899:946.496   Data:  00 08 00 20
T2228 8899:946.496 - 0.482ms returns 4 (0x4)
T6778 8901:804.032 JLINK_ReadMemEx(0x00008308, 0x2 Bytes, Flags = 0x02000000)
T6778 8901:804.032   CPU_ReadMem(64 bytes @ 0x00008300)
T6778 8901:806.080    -- Updating DA cache (64 bytes @ 0x00008300)
T6778 8901:806.080    -- Read from DA cache (2 bytes @ 0x00008308)
T6778 8901:806.080   Data:  10 B5
T6778 8901:806.080 - 2.012ms returns 2 (0x2)
T6778 8901:806.080 JLINK_HasError()
T6778 8901:806.080 JLINK_HasError()
T6778 8901:806.080 JLINK_Go()
T6778 8901:806.080   CPU_ReadMem(4 bytes @ 0x********)
T6778 8901:806.080   CPU_WriteMem(4 bytes @ 0xE0002008)
T6778 8901:807.104 - 1.315ms
T6778 8901:908.480 JLINK_HasError()
T6778 8901:908.480 JLINK_IsHalted()
T6778 8901:908.480 - 0.387ms returns FALSE
T6778 8902:009.856 JLINK_HasError()
T6778 8902:009.856 JLINK_IsHalted()
T6778 8902:009.856 - 0.330ms returns FALSE
T6778 8902:110.208 JLINK_HasError()
T6778 8902:110.208 JLINK_IsHalted()
T6778 8902:110.208 - 0.324ms returns FALSE
T6778 8902:211.584 JLINK_HasError()
T6778 8902:211.584 JLINK_IsHalted()
T6778 8902:211.584 - 0.378ms returns FALSE
T6778 8902:312.960 JLINK_HasError()
T6778 8902:312.960 JLINK_IsHalted()
T6778 8902:312.960 - 0.334ms returns FALSE
T6778 8902:412.288 JLINK_HasError()
T6778 8902:412.288 JLINK_HasError()
T6778 8902:412.288 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8902:412.288   CPU_ReadMem(4 bytes @ 0xE0001004)
T6778 8902:413.312   Data:  19 41 48 07
T6778 8902:413.312   Debug reg: DWT_CYCCNT
T6778 8902:413.312 - 0.440ms returns 1 (0x1)
T2228 8902:415.360 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8902:416.384   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8902:416.384   Data:  00 08 00 20
T2228 8902:416.384 - 0.421ms returns 4 (0x4)
T2228 8902:416.384 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8902:416.384   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8902:416.384   Data:  00 08 00 20
T2228 8902:416.384 - 0.309ms returns 4 (0x4)
T6778 8902:419.456 JLINK_IsHalted()
T6778 8902:420.480 - 0.328ms returns FALSE
T6778 8902:520.832 JLINK_HasError()
T6778 8902:520.832 JLINK_IsHalted()
T6778 8902:520.832 - 0.320ms returns FALSE
T6778 8902:622.208 JLINK_HasError()
T6778 8902:622.208 JLINK_IsHalted()
T6778 8902:622.208 - 0.420ms returns FALSE
T6778 8902:723.584 JLINK_HasError()
T6778 8902:723.584 JLINK_IsHalted()
T6778 8902:723.584 - 0.365ms returns FALSE
T6778 8902:824.960 JLINK_HasError()
T6778 8902:824.960 JLINK_IsHalted()
T6778 8902:824.960 - 0.364ms returns FALSE
T6778 8902:925.312 JLINK_HasError()
T6778 8902:926.336 JLINK_HasError()
T6778 8902:926.336 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8902:926.336   Data:  19 41 48 07
T6778 8902:926.336   Debug reg: DWT_CYCCNT
T6778 8902:926.336 - 0.037ms returns 1 (0x1)
T2228 8902:931.456 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8902:931.456   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8902:931.456   Data:  00 08 00 20
T2228 8902:931.456 - 0.496ms returns 4 (0x4)
T2228 8902:932.480 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8902:932.480   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8902:932.480   Data:  00 08 00 20
T2228 8902:932.480 - 0.397ms returns 4 (0x4)
T6778 8902:935.552 JLINK_IsHalted()
T6778 8902:936.576 - 0.484ms returns FALSE
T6778 8903:035.904 JLINK_HasError()
T6778 8903:035.904 JLINK_IsHalted()
T6778 8903:035.904 - 0.401ms returns FALSE
T6778 8903:137.280 JLINK_HasError()
T6778 8903:137.280 JLINK_IsHalted()
T6778 8903:138.304 - 0.841ms returns FALSE
T6778 8903:238.656 JLINK_HasError()
T6778 8903:238.656 JLINK_IsHalted()
T6778 8903:239.680 - 0.403ms returns FALSE
T6778 8903:339.008 JLINK_HasError()
T6778 8903:339.008 JLINK_IsHalted()
T6778 8903:340.032 - 0.408ms returns FALSE
T6778 8903:440.384 JLINK_HasError()
T6778 8903:440.384 JLINK_HasError()
T6778 8903:440.384 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8903:440.384   Data:  19 41 48 07
T6778 8903:440.384   Debug reg: DWT_CYCCNT
T6778 8903:440.384 - 0.029ms returns 1 (0x1)
T2228 8903:442.432 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8903:442.432   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8903:442.432   Data:  00 08 00 20
T2228 8903:442.432 - 0.447ms returns 4 (0x4)
T2228 8903:442.432 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8903:442.432   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8903:443.456   Data:  00 08 00 20
T2228 8903:443.456 - 0.339ms returns 4 (0x4)
T6778 8903:446.528 JLINK_IsHalted()
T6778 8903:446.528 - 0.356ms returns FALSE
T6778 8903:546.880 JLINK_HasError()
T6778 8903:546.880 JLINK_IsHalted()
T6778 8903:546.880 - 0.321ms returns FALSE
T6778 8903:647.232 JLINK_HasError()
T6778 8903:647.232 JLINK_IsHalted()
T6778 8903:648.256 - 0.305ms returns FALSE
T6778 8903:748.608 JLINK_HasError()
T6778 8903:748.608 JLINK_IsHalted()
T6778 8903:748.608 - 0.621ms returns FALSE
T6778 8903:849.984 JLINK_HasError()
T6778 8903:849.984 JLINK_IsHalted()
T6778 8903:851.008 - 0.326ms returns FALSE
T6778 8903:952.384 JLINK_HasError()
T6778 8903:952.384 JLINK_IsHalted()
T6778 8903:952.384 - 0.412ms returns FALSE
T6778 8904:052.736 JLINK_HasError()
T6778 8904:052.736 JLINK_HasError()
T6778 8904:052.736 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8904:052.736   Data:  19 41 48 07
T6778 8904:052.736   Debug reg: DWT_CYCCNT
T6778 8904:052.736 - 0.029ms returns 1 (0x1)
T2228 8904:056.832 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8904:056.832   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8904:056.832   Data:  00 08 00 20
T2228 8904:056.832 - 0.369ms returns 4 (0x4)
T2228 8904:056.832 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8904:056.832   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8904:057.856   Data:  00 08 00 20
T2228 8904:057.856 - 0.305ms returns 4 (0x4)
T6778 8904:059.904 JLINK_IsHalted()
T6778 8904:059.904 - 0.307ms returns FALSE
T6778 8904:161.280 JLINK_HasError()
T6778 8904:161.280 JLINK_IsHalted()
T6778 8904:161.280 - 0.380ms returns FALSE
T6778 8904:261.632 JLINK_HasError()
T6778 8904:261.632 JLINK_IsHalted()
T6778 8904:262.656 - 0.920ms returns FALSE
T6778 8904:363.008 JLINK_HasError()
T6778 8904:363.008 JLINK_IsHalted()
T6778 8904:363.008 - 0.344ms returns FALSE
T6778 8904:464.384 JLINK_HasError()
T6778 8904:464.384 JLINK_IsHalted()
T6778 8904:464.384 - 0.615ms returns FALSE
T2228 8904:468.480 JLINK_HasError()
T2228 8904:468.480 JLINK_SetBPEx(Addr = 0x0000A80A, Type = 0xFFFFFFF2)
T2228 8904:468.480   CPU is running
T2228 8904:468.480   CPU is running
T2228 8904:468.480   CPU_WriteMem(4 bytes @ 0x********)
T2228 8904:468.480   CPU is running
T2228 8904:468.480   CPU_WriteMem(4 bytes @ 0xE0002008)
T2228 8904:468.480 - 0.808ms returns 0x00000002
T6778 8904:564.736 JLINK_HasError()
T6778 8904:564.736 JLINK_HasError()
T6778 8904:564.736 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8904:564.736   Data:  19 41 48 07
T6778 8904:564.736   Debug reg: DWT_CYCCNT
T6778 8904:564.736 - 0.041ms returns 1 (0x1)
T2228 8904:570.880 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8904:570.880   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8904:571.904   Data:  00 08 00 20
T2228 8904:571.904 - 0.471ms returns 4 (0x4)
T2228 8904:571.904 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8904:571.904   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8904:572.928   Data:  00 08 00 20
T2228 8904:572.928 - 0.376ms returns 4 (0x4)
T6778 8904:574.976 JLINK_IsHalted()
T6778 8904:576.000 - 0.449ms returns FALSE
T6778 8904:676.352 JLINK_HasError()
T6778 8904:676.352 JLINK_IsHalted()
T6778 8904:677.376 - 0.529ms returns FALSE
T6778 8904:777.728 JLINK_HasError()
T6778 8904:777.728 JLINK_IsHalted()
T6778 8904:778.752 - 0.818ms returns FALSE
T6778 8904:879.104 JLINK_HasError()
T6778 8904:879.104 JLINK_IsHalted()
T6778 8904:880.128 - 0.466ms returns FALSE
T6778 8904:980.480 JLINK_HasError()
T6778 8904:980.480 JLINK_IsHalted()
T6778 8904:980.480 - 0.335ms returns FALSE
T6778 8905:081.856 JLINK_HasError()
T6778 8905:081.856 JLINK_HasError()
T6778 8905:081.856 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8905:081.856   Data:  19 41 48 07
T6778 8905:081.856   Debug reg: DWT_CYCCNT
T6778 8905:081.856 - 0.029ms returns 1 (0x1)
T2228 8905:082.880 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8905:082.880   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8905:083.904   Data:  00 08 00 20
T2228 8905:083.904 - 0.566ms returns 4 (0x4)
T2228 8905:083.904 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8905:083.904   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8905:083.904   Data:  00 08 00 20
T2228 8905:083.904 - 0.524ms returns 4 (0x4)
T6778 8905:089.024 JLINK_IsHalted()
T6778 8905:089.024 - 0.410ms returns FALSE
T6778 8905:189.376 JLINK_HasError()
T6778 8905:189.376 JLINK_IsHalted()
T6778 8905:189.376 - 0.389ms returns FALSE
T6778 8905:290.752 JLINK_HasError()
T6778 8905:290.752 JLINK_IsHalted()
T6778 8905:290.752 - 0.307ms returns FALSE
T6778 8905:391.104 JLINK_HasError()
T6778 8905:391.104 JLINK_IsHalted()
T6778 8905:392.128 - 0.346ms returns FALSE
T6778 8905:492.480 JLINK_HasError()
T6778 8905:492.480 JLINK_IsHalted()
T6778 8905:492.480 - 0.323ms returns FALSE
T6778 8905:593.856 JLINK_HasError()
T6778 8905:593.856 JLINK_IsHalted()
T6778 8905:594.880 - 0.426ms returns FALSE
T6778 8905:695.232 JLINK_HasError()
T6778 8905:695.232 JLINK_HasError()
T6778 8905:695.232 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8905:695.232   Data:  19 41 48 07
T6778 8905:695.232   Debug reg: DWT_CYCCNT
T6778 8905:695.232 - 0.029ms returns 1 (0x1)
T2228 8905:697.280 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8905:697.280   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8905:697.280   Data:  00 08 00 20
T2228 8905:697.280 - 0.395ms returns 4 (0x4)
T2228 8905:697.280 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8905:697.280   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8905:698.304   Data:  00 08 00 20
T2228 8905:698.304 - 0.358ms returns 4 (0x4)
T6778 8905:701.376 JLINK_IsHalted()
T6778 8905:701.376 - 0.314ms returns FALSE
T6778 8905:802.752 JLINK_HasError()
T6778 8905:802.752 JLINK_IsHalted()
T6778 8905:802.752 - 0.356ms returns FALSE
T6778 8905:903.104 JLINK_HasError()
T6778 8905:903.104 JLINK_IsHalted()
T6778 8905:903.104 - 0.316ms returns FALSE
T6778 8906:004.480 JLINK_HasError()
T6778 8906:004.480 JLINK_IsHalted()
T6778 8906:004.480 - 0.273ms returns FALSE
T6778 8906:104.832 JLINK_HasError()
T6778 8906:104.832 JLINK_IsHalted()
T6778 8906:104.832 - 0.525ms returns FALSE
T6778 8906:206.208 JLINK_HasError()
T6778 8906:206.208 JLINK_HasError()
T6778 8906:206.208 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8906:206.208   Data:  19 41 48 07
T6778 8906:206.208   Debug reg: DWT_CYCCNT
T6778 8906:206.208 - 0.082ms returns 1 (0x1)
T2228 8906:208.256 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8906:208.256   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8906:208.256   Data:  00 08 00 20
T2228 8906:208.256 - 0.451ms returns 4 (0x4)
T2228 8906:209.280 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8906:209.280   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8906:209.280   Data:  00 08 00 20
T2228 8906:209.280 - 0.378ms returns 4 (0x4)
T6778 8906:212.352 JLINK_IsHalted()
T6778 8906:213.376 - 0.413ms returns FALSE
T6778 8906:312.704 JLINK_HasError()
T6778 8906:312.704 JLINK_IsHalted()
T6778 8906:312.704 - 0.317ms returns FALSE
T6778 8906:414.080 JLINK_HasError()
T6778 8906:414.080 JLINK_IsHalted()
T6778 8906:414.080 - 0.344ms returns FALSE
T6778 8906:515.456 JLINK_HasError()
T6778 8906:515.456 JLINK_IsHalted()
T6778 8906:516.480 - 0.942ms returns FALSE
T6778 8906:616.832 JLINK_HasError()
T6778 8906:616.832 JLINK_IsHalted()
T6778 8906:616.832 - 0.390ms returns FALSE
T6778 8906:717.184 JLINK_HasError()
T6778 8906:717.184 JLINK_IsHalted()
T6778 8906:717.184 - 0.358ms returns FALSE
T6778 8906:818.560 JLINK_HasError()
T6778 8906:818.560 JLINK_HasError()
T6778 8906:818.560 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8906:818.560   Data:  19 41 48 07
T6778 8906:818.560   Debug reg: DWT_CYCCNT
T6778 8906:818.560 - 0.028ms returns 1 (0x1)
T2228 8906:819.584 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8906:819.584   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8906:820.608   Data:  00 08 00 20
T2228 8906:820.608 - 0.425ms returns 4 (0x4)
T2228 8906:820.608 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8906:820.608   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8906:821.632   Data:  00 08 00 20
T2228 8906:821.632 - 0.459ms returns 4 (0x4)
T6778 8906:823.680 JLINK_IsHalted()
T6778 8906:824.704 - 0.435ms returns FALSE
T6778 8906:926.080 JLINK_HasError()
T6778 8906:926.080 JLINK_IsHalted()
T6778 8906:926.080 - 0.310ms returns FALSE
T2228 8906:962.944 JLINK_HasError()
T2228 8906:962.944 JLINK_ClrBPEx(BPHandle = 0x00000002)
T2228 8906:962.944   CPU is running
T2228 8906:962.944   CPU is running
T2228 8906:962.944   CPU_WriteMem(4 bytes @ 0xE0002008)
T2228 8906:962.944   CPU is running
T2228 8906:962.944   CPU_WriteMem(4 bytes @ 0xE000200C)
T2228 8906:963.968   CPU is running
T2228 8906:963.968   CPU_WriteMem(4 bytes @ 0xE0002010)
T2228 8906:963.968   CPU is running
T2228 8906:963.968   CPU_WriteMem(4 bytes @ 0xE0002014)
T2228 8906:963.968   CPU is running
T2228 8906:963.968   CPU_WriteMem(4 bytes @ 0xE0002018)
T2228 8906:964.992   CPU is running
T2228 8906:964.992   CPU_WriteMem(4 bytes @ 0xE000201C)
T2228 8906:964.992 - 2.501ms returns 0x00
T6778 8907:026.432 JLINK_HasError()
T6778 8907:026.432 JLINK_IsHalted()
T6778 8907:027.456 - 0.322ms returns FALSE
T6778 8907:127.808 JLINK_HasError()
T6778 8907:127.808 JLINK_IsHalted()
T6778 8907:127.808 - 0.493ms returns FALSE
T6778 8907:229.184 JLINK_HasError()
T6778 8907:229.184 JLINK_IsHalted()
T6778 8907:229.184 - 0.326ms returns FALSE
T6778 8907:329.536 JLINK_HasError()
T6778 8907:329.536 JLINK_IsHalted()
T6778 8907:330.560 - 0.893ms returns FALSE
T2228 8907:396.096 JLINK_HasError()
T2228 8907:396.096 JLINK_SetBPEx(Addr = 0x0000A80A, Type = 0xFFFFFFF2)
T2228 8907:396.096   CPU is running
T2228 8907:396.096   CPU is running
T2228 8907:396.096   CPU_WriteMem(4 bytes @ 0x********)
T2228 8907:397.120   CPU is running
T2228 8907:397.120   CPU_WriteMem(4 bytes @ 0xE0002008)
T2228 8907:397.120 - 0.851ms returns 0x00000003
T6778 8907:430.912 JLINK_HasError()
T6778 8907:430.912 JLINK_HasError()
T6778 8907:430.912 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8907:430.912   Data:  19 41 48 07
T6778 8907:431.936   Debug reg: DWT_CYCCNT
T6778 8907:431.936 - 0.033ms returns 1 (0x1)
T2228 8907:433.984 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8907:433.984   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8907:433.984   Data:  00 08 00 20
T2228 8907:433.984 - 0.427ms returns 4 (0x4)
T2228 8907:433.984 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8907:433.984   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8907:433.984   Data:  00 08 00 20
T2228 8907:433.984 - 0.315ms returns 4 (0x4)
T6778 8907:438.080 JLINK_IsHalted()
T6778 8907:438.080 - 0.364ms returns FALSE
T6778 8907:538.432 JLINK_HasError()
T6778 8907:539.456 JLINK_IsHalted()
T6778 8907:539.456 - 0.518ms returns FALSE
T6778 8907:639.808 JLINK_HasError()
T6778 8907:639.808 JLINK_IsHalted()
T6778 8907:640.832 - 0.382ms returns FALSE
T6778 8907:741.184 JLINK_HasError()
T6778 8907:741.184 JLINK_IsHalted()
T6778 8907:742.208 - 0.416ms returns FALSE
T6778 8907:843.584 JLINK_HasError()
T6778 8907:843.584 JLINK_IsHalted()
T6778 8907:843.584 - 0.496ms returns FALSE
T6778 8907:944.960 JLINK_HasError()
T6778 8907:944.960 JLINK_HasError()
T6778 8907:944.960 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8907:944.960   Data:  19 41 48 07
T6778 8907:944.960   Debug reg: DWT_CYCCNT
T6778 8907:944.960 - 0.028ms returns 1 (0x1)
T2228 8907:947.008 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8907:947.008   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8907:947.008   Data:  00 08 00 20
T2228 8907:947.008 - 0.371ms returns 4 (0x4)
T2228 8907:947.008 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8907:947.008   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8907:948.032   Data:  00 08 00 20
T2228 8907:948.032 - 0.355ms returns 4 (0x4)
T6778 8907:950.080 JLINK_IsHalted()
T6778 8907:951.104 - 0.910ms returns FALSE
T6778 8908:052.480 JLINK_HasError()
T6778 8908:052.480 JLINK_IsHalted()
T6778 8908:052.480 - 0.322ms returns FALSE
T6778 8908:152.832 JLINK_HasError()
T6778 8908:152.832 JLINK_IsHalted()
T6778 8908:152.832 - 0.311ms returns FALSE
T6778 8908:254.208 JLINK_HasError()
T6778 8908:254.208 JLINK_IsHalted()
T6778 8908:254.208 - 0.329ms returns FALSE
T6778 8908:355.584 JLINK_HasError()
T6778 8908:355.584 JLINK_IsHalted()
T6778 8908:355.584 - 0.314ms returns FALSE
T6778 8908:455.936 JLINK_HasError()
T6778 8908:455.936 JLINK_HasError()
T6778 8908:455.936 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8908:455.936   Data:  19 41 48 07
T6778 8908:455.936   Debug reg: DWT_CYCCNT
T6778 8908:455.936 - 0.028ms returns 1 (0x1)
T2228 8908:457.984 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8908:457.984   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8908:457.984   Data:  00 08 00 20
T2228 8908:457.984 - 0.426ms returns 4 (0x4)
T2228 8908:457.984 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8908:457.984   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8908:457.984   Data:  00 08 00 20
T2228 8908:457.984 - 0.304ms returns 4 (0x4)
T6778 8908:462.080 JLINK_IsHalted()
T6778 8908:462.080 - 0.955ms returns FALSE
T6778 8908:562.432 JLINK_HasError()
T6778 8908:562.432 JLINK_IsHalted()
T6778 8908:563.456 - 0.347ms returns FALSE
T6778 8908:662.784 JLINK_HasError()
T6778 8908:662.784 JLINK_IsHalted()
T6778 8908:664.832 - 1.027ms returns FALSE
T6778 8908:765.184 JLINK_HasError()
T6778 8908:765.184 JLINK_IsHalted()
T6778 8908:765.184 - 0.290ms returns FALSE
T6778 8908:866.560 JLINK_HasError()
T6778 8908:866.560 JLINK_IsHalted()
T6778 8908:866.560 - 0.326ms returns FALSE
T6778 8908:966.912 JLINK_HasError()
T6778 8908:966.912 JLINK_IsHalted()
T6778 8908:967.936 - 0.362ms returns FALSE
T6778 8909:068.288 JLINK_HasError()
T6778 8909:068.288 JLINK_HasError()
T6778 8909:068.288 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8909:068.288   Data:  19 41 48 07
T6778 8909:068.288   Debug reg: DWT_CYCCNT
T6778 8909:068.288 - 0.030ms returns 1 (0x1)
T2228 8909:070.336 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8909:070.336   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8909:071.360   Data:  00 08 00 20
T2228 8909:071.360 - 1.019ms returns 4 (0x4)
T2228 8909:072.384 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8909:072.384   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8909:072.384   Data:  00 08 00 20
T2228 8909:072.384 - 0.517ms returns 4 (0x4)
T6778 8909:075.456 JLINK_IsHalted()
T6778 8909:075.456 - 0.535ms returns FALSE
T6778 8909:176.832 JLINK_HasError()
T6778 8909:176.832 JLINK_IsHalted()
T6778 8909:176.832 - 0.382ms returns FALSE
T2228 8909:268.992 JLINK_HasError()
T2228 8909:268.992 JLINK_SetBPEx(Addr = 0x0000A7DA, Type = 0xFFFFFFF2)
T2228 8909:268.992   CPU is running
T2228 8909:268.992   CPU is running
T2228 8909:268.992   CPU_WriteMem(4 bytes @ 0x********)
T2228 8909:268.992   CPU is running
T2228 8909:268.992   CPU_WriteMem(4 bytes @ 0xE000200C)
T2228 8909:270.016 - 0.640ms returns 0x00000004
T6778 8909:277.184 JLINK_HasError()
T6778 8909:277.184 JLINK_IsHalted()
T6778 8909:278.208 - 0.334ms returns FALSE
T6778 8909:378.560 JLINK_HasError()
T6778 8909:378.560 JLINK_IsHalted()
T6778 8909:378.560 - 0.416ms returns FALSE
T6778 8909:478.912 JLINK_HasError()
T6778 8909:478.912 JLINK_IsHalted()
T6778 8909:479.936 - 0.468ms returns FALSE
T6778 8909:580.288 JLINK_HasError()
T6778 8909:580.288 JLINK_HasError()
T6778 8909:580.288 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8909:580.288   Data:  19 41 48 07
T6778 8909:580.288   Debug reg: DWT_CYCCNT
T6778 8909:580.288 - 0.027ms returns 1 (0x1)
T2228 8909:582.336 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8909:582.336   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8909:582.336   Data:  00 08 00 20
T2228 8909:582.336 - 0.419ms returns 4 (0x4)
T2228 8909:582.336 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8909:582.336   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8909:583.360   Data:  00 08 00 20
T2228 8909:583.360 - 0.430ms returns 4 (0x4)
T6778 8909:586.432 JLINK_IsHalted()
T6778 8909:586.432 - 0.440ms returns FALSE
T6778 8909:688.832 JLINK_HasError()
T6778 8909:688.832 JLINK_IsHalted()
T6778 8909:688.832 - 0.310ms returns FALSE
T6778 8909:789.184 JLINK_HasError()
T6778 8909:789.184 JLINK_IsHalted()
T6778 8909:790.208 - 0.299ms returns FALSE
T6778 8909:890.560 JLINK_HasError()
T6778 8909:890.560 JLINK_IsHalted()
T6778 8909:890.560 - 0.397ms returns FALSE
T6778 8909:991.936 JLINK_HasError()
T6778 8909:991.936 JLINK_IsHalted()
T6778 8909:991.936 - 0.378ms returns FALSE
T6778 8910:092.288 JLINK_HasError()
T6778 8910:092.288 JLINK_IsHalted()
T6778 8910:092.288 - 0.409ms returns FALSE
T6778 8910:193.664 JLINK_HasError()
T6778 8910:193.664 JLINK_HasError()
T6778 8910:193.664 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8910:193.664   Data:  19 41 48 07
T6778 8910:193.664   Debug reg: DWT_CYCCNT
T6778 8910:193.664 - 0.029ms returns 1 (0x1)
T2228 8910:196.736 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8910:196.736   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8910:197.760   Data:  00 08 00 20
T2228 8910:197.760 - 0.423ms returns 4 (0x4)
T2228 8910:197.760 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8910:197.760   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8910:197.760   Data:  00 08 00 20
T2228 8910:197.760 - 0.313ms returns 4 (0x4)
T6778 8910:200.832 JLINK_IsHalted()
T6778 8910:200.832 - 0.331ms returns FALSE
T6778 8910:302.208 JLINK_HasError()
T6778 8910:302.208 JLINK_IsHalted()
T6778 8910:302.208 - 0.316ms returns FALSE
T6778 8910:403.584 JLINK_HasError()
T6778 8910:403.584 JLINK_IsHalted()
T6778 8910:403.584 - 0.444ms returns FALSE
T6778 8910:503.936 JLINK_HasError()
T6778 8910:503.936 JLINK_IsHalted()
T6778 8910:503.936 - 0.404ms returns FALSE
T6778 8910:604.288 JLINK_HasError()
T6778 8910:604.288 JLINK_IsHalted()
T6778 8910:605.312 - 0.524ms returns FALSE
T6778 8910:706.688 JLINK_HasError()
T6778 8910:706.688 JLINK_HasError()
T6778 8910:706.688 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8910:706.688   Data:  19 41 48 07
T6778 8910:706.688   Debug reg: DWT_CYCCNT
T6778 8910:706.688 - 0.066ms returns 1 (0x1)
T2228 8910:710.784 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8910:710.784   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8910:710.784   Data:  00 08 00 20
T2228 8910:710.784 - 0.360ms returns 4 (0x4)
T2228 8910:710.784 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8910:710.784   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8910:710.784   Data:  00 08 00 20
T2228 8910:710.784 - 0.334ms returns 4 (0x4)
T6778 8910:714.880 JLINK_IsHalted()
T6778 8910:714.880 - 0.417ms returns FALSE
T6778 8910:816.256 JLINK_HasError()
T6778 8910:816.256 JLINK_IsHalted()
T6778 8910:817.280 - 0.596ms returns FALSE
T6778 8910:918.656 JLINK_HasError()
T6778 8910:918.656 JLINK_IsHalted()
T6778 8910:918.656 - 0.450ms returns FALSE
T6778 8911:019.008 JLINK_HasError()
T6778 8911:019.008 JLINK_IsHalted()
T6778 8911:020.032 - 0.341ms returns FALSE
T6778 8911:120.384 JLINK_HasError()
T6778 8911:120.384 JLINK_IsHalted()
T6778 8911:121.408 - 0.590ms returns FALSE
T6778 8911:222.784 JLINK_HasError()
T6778 8911:222.784 JLINK_HasError()
T6778 8911:222.784 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8911:222.784   Data:  19 41 48 07
T6778 8911:222.784   Debug reg: DWT_CYCCNT
T6778 8911:222.784 - 0.053ms returns 1 (0x1)
T2228 8911:225.856 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8911:225.856   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8911:225.856   Data:  00 08 00 20
T2228 8911:225.856 - 0.530ms returns 4 (0x4)
T2228 8911:226.880 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8911:226.880   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8911:226.880   Data:  00 08 00 20
T2228 8911:226.880 - 0.537ms returns 4 (0x4)
T6778 8911:230.976 JLINK_IsHalted()
T6778 8911:230.976 - 0.546ms returns FALSE
T6778 8911:332.352 JLINK_HasError()
T6778 8911:332.352 JLINK_IsHalted()
T6778 8911:332.352 - 0.497ms returns FALSE
T2228 8911:373.312 JLINK_HasError()
T2228 8911:373.312 JLINK_ClrBPEx(BPHandle = 0x00000003)
T2228 8911:374.336   CPU is running
T2228 8911:374.336   CPU is running
T2228 8911:374.336   CPU_WriteMem(4 bytes @ 0x********)
T2228 8911:374.336   CPU is running
T2228 8911:374.336   CPU_WriteMem(4 bytes @ 0xE000200C)
T2228 8911:374.336   CPU is running
T2228 8911:374.336   CPU_WriteMem(4 bytes @ 0xE0002008)
T2228 8911:374.336   CPU is running
T2228 8911:374.336   CPU_WriteMem(4 bytes @ 0xE0002008)
T2228 8911:375.360   CPU is running
T2228 8911:375.360   CPU_WriteMem(4 bytes @ 0xE000200C)
T2228 8911:375.360   CPU is running
T2228 8911:375.360   CPU_WriteMem(4 bytes @ 0xE0002010)
T2228 8911:376.384   CPU is running
T2228 8911:376.384   CPU_WriteMem(4 bytes @ 0xE0002014)
T2228 8911:376.384   CPU is running
T2228 8911:376.384   CPU_WriteMem(4 bytes @ 0xE0002018)
T2228 8911:376.384   CPU is running
T2228 8911:376.384   CPU_WriteMem(4 bytes @ 0xE000201C)
T2228 8911:377.408 - 3.706ms returns 0x00
T6778 8911:432.704 JLINK_HasError()
T6778 8911:432.704 JLINK_IsHalted()
T6778 8911:433.728 - 0.503ms returns FALSE
T6778 8911:534.080 JLINK_HasError()
T6778 8911:534.080 JLINK_IsHalted()
T6778 8911:534.080 - 0.368ms returns FALSE
T6778 8911:635.456 JLINK_HasError()
T6778 8911:635.456 JLINK_IsHalted()
T6778 8911:635.456 - 0.498ms returns FALSE
T6778 8911:735.808 JLINK_HasError()
T6778 8911:735.808 JLINK_HasError()
T6778 8911:735.808 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8911:735.808   Data:  19 41 48 07
T6778 8911:735.808   Debug reg: DWT_CYCCNT
T6778 8911:735.808 - 0.031ms returns 1 (0x1)
T2228 8911:739.904 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8911:739.904   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8911:740.928   Data:  00 08 00 20
T2228 8911:740.928 - 0.367ms returns 4 (0x4)
T2228 8911:740.928 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8911:740.928   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8911:740.928   Data:  00 08 00 20
T2228 8911:740.928 - 0.336ms returns 4 (0x4)
T6778 8911:744.000 JLINK_IsHalted()
T6778 8911:744.000 - 0.362ms returns FALSE
T6778 8911:845.376 JLINK_HasError()
T6778 8911:845.376 JLINK_IsHalted()
T6778 8911:845.376 - 0.338ms returns FALSE
T6778 8911:944.704 JLINK_HasError()
T6778 8911:944.704 JLINK_IsHalted()
T6778 8911:945.728 - 0.361ms returns FALSE
T6778 8912:046.080 JLINK_HasError()
T6778 8912:046.080 JLINK_IsHalted()
T6778 8912:046.080 - 0.499ms returns FALSE
T6778 8912:147.456 JLINK_HasError()
T6778 8912:147.456 JLINK_IsHalted()
T6778 8912:147.456 - 0.323ms returns FALSE
T6778 8912:247.808 JLINK_HasError()
T6778 8912:247.808 JLINK_IsHalted()
T6778 8912:248.832 - 0.352ms returns FALSE
T6778 8912:349.184 JLINK_HasError()
T6778 8912:349.184 JLINK_HasError()
T6778 8912:349.184 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8912:349.184   Data:  19 41 48 07
T6778 8912:349.184   Debug reg: DWT_CYCCNT
T6778 8912:349.184 - 0.040ms returns 1 (0x1)
T2228 8912:353.280 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8912:353.280   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8912:354.304   Data:  00 08 00 20
T2228 8912:354.304 - 0.388ms returns 4 (0x4)
T2228 8912:354.304 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8912:354.304   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8912:354.304   Data:  00 08 00 20
T2228 8912:354.304 - 0.385ms returns 4 (0x4)
T6778 8912:357.376 JLINK_IsHalted()
T6778 8912:358.400 - 0.314ms returns FALSE
T6778 8912:459.776 JLINK_HasError()
T6778 8912:459.776 JLINK_IsHalted()
T6778 8912:459.776 - 0.436ms returns FALSE
T6778 8912:561.152 JLINK_HasError()
T6778 8912:561.152 JLINK_IsHalted()
T6778 8912:561.152 - 0.317ms returns FALSE
T6778 8912:662.528 JLINK_HasError()
T6778 8912:662.528 JLINK_IsHalted()
T6778 8912:662.528 - 0.451ms returns FALSE
T6778 8912:762.880 JLINK_HasError()
T6778 8912:762.880 JLINK_IsHalted()
T6778 8912:763.904 - 0.518ms returns FALSE
T6778 8912:865.280 JLINK_HasError()
T6778 8912:865.280 JLINK_HasError()
T6778 8912:865.280 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8912:865.280   Data:  19 41 48 07
T6778 8912:865.280   Debug reg: DWT_CYCCNT
T6778 8912:865.280 - 0.042ms returns 1 (0x1)
T2228 8912:869.376 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8912:869.376   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8912:870.400   Data:  00 08 00 20
T2228 8912:870.400 - 0.446ms returns 4 (0x4)
T2228 8912:870.400 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8912:870.400   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8912:870.400   Data:  00 08 00 20
T2228 8912:870.400 - 0.350ms returns 4 (0x4)
T6778 8912:873.472 JLINK_IsHalted()
T6778 8912:874.496 - 0.328ms returns FALSE
T6778 8912:974.848 JLINK_HasError()
T6778 8912:974.848 JLINK_IsHalted()
T6778 8912:975.872 - 0.415ms returns FALSE
T6778 8913:075.200 JLINK_HasError()
T6778 8913:075.200 JLINK_IsHalted()
T6778 8913:076.224 - 0.426ms returns FALSE
T6778 8913:176.576 JLINK_HasError()
T6778 8913:176.576 JLINK_IsHalted()
T6778 8913:176.576 - 0.357ms returns FALSE
T6778 8913:277.952 JLINK_HasError()
T6778 8913:277.952 JLINK_IsHalted()
T6778 8913:277.952 - 0.413ms returns FALSE
T6778 8913:378.304 JLINK_HasError()
T6778 8913:378.304 JLINK_HasError()
T6778 8913:378.304 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8913:378.304   Data:  19 41 48 07
T6778 8913:378.304   Debug reg: DWT_CYCCNT
T6778 8913:378.304 - 0.173ms returns 1 (0x1)
T2228 8913:382.400 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8913:382.400   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8913:382.400   Data:  00 08 00 20
T2228 8913:382.400 - 0.426ms returns 4 (0x4)
T2228 8913:383.424 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8913:383.424   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8913:383.424   Data:  00 08 00 20
T2228 8913:383.424 - 0.312ms returns 4 (0x4)
T6778 8913:386.496 JLINK_IsHalted()
T6778 8913:387.520 - 0.380ms returns FALSE
T6778 8913:487.872 JLINK_HasError()
T6778 8913:487.872 JLINK_IsHalted()
T6778 8913:488.896 - 0.395ms returns FALSE
T6778 8913:590.272 JLINK_HasError()
T6778 8913:590.272 JLINK_IsHalted()
T6778 8913:590.272 - 0.314ms returns FALSE
T6778 8913:691.648 JLINK_HasError()
T6778 8913:691.648 JLINK_IsHalted()
T6778 8913:691.648 - 0.393ms returns FALSE
T6778 8913:792.000 JLINK_HasError()
T6778 8913:792.000 JLINK_IsHalted()
T6778 8913:793.024 - 0.363ms returns FALSE
T6778 8913:893.376 JLINK_HasError()
T6778 8913:893.376 JLINK_HasError()
T6778 8913:893.376 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8913:893.376   Data:  19 41 48 07
T6778 8913:893.376   Debug reg: DWT_CYCCNT
T6778 8913:893.376 - 0.047ms returns 1 (0x1)
T2228 8913:897.472 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8913:897.472   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8913:897.472   Data:  00 08 00 20
T2228 8913:897.472 - 0.365ms returns 4 (0x4)
T2228 8913:897.472 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8913:897.472   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8913:897.472   Data:  00 08 00 20
T2228 8913:897.472 - 0.413ms returns 4 (0x4)
T6778 8913:900.544 JLINK_IsHalted()
T6778 8913:901.568 - 0.402ms returns FALSE
T6778 8914:001.920 JLINK_HasError()
T6778 8914:001.920 JLINK_IsHalted()
T6778 8914:001.920 - 0.387ms returns FALSE
T6778 8914:103.296 JLINK_HasError()
T6778 8914:103.296 JLINK_IsHalted()
T6778 8914:103.296 - 0.332ms returns FALSE
T6778 8914:204.672 JLINK_HasError()
T6778 8914:204.672 JLINK_IsHalted()
T6778 8914:204.672 - 0.547ms returns FALSE
T6778 8914:306.048 JLINK_HasError()
T6778 8914:306.048 JLINK_IsHalted()
T6778 8914:307.072 - 0.465ms returns FALSE
T6778 8914:407.424 JLINK_HasError()
T6778 8914:407.424 JLINK_IsHalted()
T6778 8914:408.448 - 0.343ms returns FALSE
T6778 8914:508.800 JLINK_HasError()
T6778 8914:508.800 JLINK_HasError()
T6778 8914:508.800 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8914:508.800   Data:  19 41 48 07
T6778 8914:508.800   Debug reg: DWT_CYCCNT
T6778 8914:508.800 - 0.059ms returns 1 (0x1)
T2228 8914:515.968 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8914:515.968   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8914:515.968   Data:  00 08 00 20
T2228 8914:515.968 - 0.531ms returns 4 (0x4)
T2228 8914:515.968 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8914:515.968   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8914:515.968   Data:  00 08 00 20
T2228 8914:515.968 - 0.456ms returns 4 (0x4)
T6778 8914:520.064 JLINK_IsHalted()
T6778 8914:520.064 - 0.407ms returns FALSE
T6778 8914:620.416 JLINK_HasError()
T6778 8914:620.416 JLINK_IsHalted()
T6778 8914:620.416 - 0.491ms returns FALSE
T6778 8914:721.792 JLINK_HasError()
T6778 8914:721.792 JLINK_IsHalted()
T6778 8914:721.792 - 0.475ms returns FALSE
T6778 8914:823.168 JLINK_HasError()
T6778 8914:823.168 JLINK_IsHalted()
T6778 8914:823.168 - 0.423ms returns FALSE
T6778 8914:924.544 JLINK_HasError()
T6778 8914:924.544 JLINK_IsHalted()
T6778 8914:924.544 - 0.537ms returns FALSE
T6778 8915:026.944 JLINK_HasError()
T6778 8915:026.944 JLINK_HasError()
T6778 8915:026.944 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8915:026.944   Data:  19 41 48 07
T6778 8915:026.944   Debug reg: DWT_CYCCNT
T6778 8915:026.944 - 0.032ms returns 1 (0x1)
T2228 8915:027.968 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8915:027.968   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8915:028.992   Data:  00 08 00 20
T2228 8915:028.992 - 0.651ms returns 4 (0x4)
T2228 8915:028.992 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8915:028.992   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8915:030.016   Data:  00 08 00 20
T2228 8915:030.016 - 0.698ms returns 4 (0x4)
T6778 8915:033.088 JLINK_IsHalted()
T6778 8915:034.112 - 0.799ms returns FALSE
T6778 8915:134.464 JLINK_HasError()
T6778 8915:134.464 JLINK_IsHalted()
T6778 8915:134.464 - 0.456ms returns FALSE
T6778 8915:236.864 JLINK_HasError()
T6778 8915:236.864 JLINK_IsHalted()
T6778 8915:236.864 - 0.574ms returns FALSE
T6778 8915:337.216 JLINK_HasError()
T6778 8915:337.216 JLINK_IsHalted()
T6778 8915:338.240 - 0.406ms returns FALSE
T6778 8915:438.592 JLINK_HasError()
T6778 8915:438.592 JLINK_IsHalted()
T6778 8915:438.592 - 0.533ms returns FALSE
T6778 8915:539.968 JLINK_HasError()
T6778 8915:539.968 JLINK_HasError()
T6778 8915:539.968 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8915:539.968   Data:  19 41 48 07
T6778 8915:539.968   Debug reg: DWT_CYCCNT
T6778 8915:539.968 - 0.024ms returns 1 (0x1)
T2228 8915:540.992 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8915:540.992   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8915:540.992   Data:  00 08 00 20
T2228 8915:540.992 - 0.498ms returns 4 (0x4)
T2228 8915:540.992 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8915:540.992   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8915:542.016   Data:  00 08 00 20
T2228 8915:542.016 - 0.326ms returns 4 (0x4)
T6778 8915:545.088 JLINK_IsHalted()
T6778 8915:545.088 - 0.375ms returns FALSE
T6778 8915:646.464 JLINK_HasError()
T6778 8915:646.464 JLINK_IsHalted()
T6778 8915:646.464 - 0.358ms returns FALSE
T6778 8915:747.840 JLINK_HasError()
T6778 8915:747.840 JLINK_IsHalted()
T6778 8915:748.864 - 0.412ms returns FALSE
T6778 8915:848.192 JLINK_HasError()
T6778 8915:848.192 JLINK_IsHalted()
T6778 8915:849.216 - 0.326ms returns FALSE
T6778 8915:949.568 JLINK_HasError()
T6778 8915:949.568 JLINK_IsHalted()
T6778 8915:949.568 - 0.505ms returns FALSE
T6778 8916:049.920 JLINK_HasError()
T6778 8916:049.920 JLINK_HasError()
T6778 8916:049.920 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8916:050.944   Data:  19 41 48 07
T6778 8916:050.944   Debug reg: DWT_CYCCNT
T6778 8916:050.944 - 0.035ms returns 1 (0x1)
T2228 8916:055.040 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8916:055.040   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8916:055.040   Data:  00 08 00 20
T2228 8916:055.040 - 0.431ms returns 4 (0x4)
T2228 8916:055.040 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8916:055.040   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8916:056.064   Data:  00 08 00 20
T2228 8916:056.064 - 0.415ms returns 4 (0x4)
T6778 8916:058.112 JLINK_IsHalted()
T6778 8916:059.136 - 0.420ms returns FALSE
T6778 8916:159.488 JLINK_HasError()
T6778 8916:159.488 JLINK_IsHalted()
T6778 8916:159.488 - 0.450ms returns FALSE
T6778 8916:260.864 JLINK_HasError()
T6778 8916:260.864 JLINK_IsHalted()
T6778 8916:260.864 - 0.372ms returns FALSE
T6778 8916:362.240 JLINK_HasError()
T6778 8916:362.240 JLINK_IsHalted()
T6778 8916:363.264 - 0.496ms returns FALSE
T6778 8916:462.592 JLINK_HasError()
T6778 8916:462.592 JLINK_IsHalted()
T6778 8916:463.616 - 0.312ms returns FALSE
T6778 8916:563.968 JLINK_HasError()
T6778 8916:563.968 JLINK_IsHalted()
T6778 8916:564.992 - 0.554ms returns FALSE
T6778 8916:665.344 JLINK_HasError()
T6778 8916:665.344 JLINK_HasError()
T6778 8916:665.344 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8916:665.344   Data:  19 41 48 07
T6778 8916:665.344   Debug reg: DWT_CYCCNT
T6778 8916:665.344 - 0.058ms returns 1 (0x1)
T2228 8916:669.440 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8916:669.440   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8916:669.440   Data:  00 08 00 20
T2228 8916:669.440 - 0.455ms returns 4 (0x4)
T2228 8916:669.440 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8916:669.440   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8916:670.464   Data:  00 08 00 20
T2228 8916:670.464 - 0.539ms returns 4 (0x4)
T6778 8916:675.584 JLINK_IsHalted()
T6778 8916:675.584 - 0.472ms returns FALSE
T6778 8916:776.960 JLINK_HasError()
T6778 8916:776.960 JLINK_IsHalted()
T6778 8916:776.960 - 0.526ms returns FALSE
T6778 8916:878.336 JLINK_HasError()
T6778 8916:878.336 JLINK_IsHalted()
T6778 8916:880.384 - 2.655ms returns TRUE
T6778 8916:880.384 JLINK_HasError()
T6778 8916:880.384 JLINK_Halt()
T6778 8916:880.384 - 0.004ms returns 0x00
T6778 8916:880.384 JLINK_IsHalted()
T6778 8916:880.384 - 0.004ms returns TRUE
T6778 8916:880.384 JLINK_IsHalted()
T6778 8916:880.384 - 0.004ms returns TRUE
T6778 8916:880.384 JLINK_IsHalted()
T6778 8916:880.384 - 0.004ms returns TRUE
T6778 8916:880.384 JLINK_HasError()
T6778 8916:880.384 JLINK_ReadReg(R15 (PC))
T6778 8916:880.384 - 0.012ms returns 0x0000A7DA
T6778 8916:880.384 JLINK_ReadReg(XPSR)
T6778 8916:880.384 - 0.004ms returns 0x21000000
T6778 8916:880.384 JLINK_HasError()
T6778 8916:880.384 JLINK_ClrBPEx(BPHandle = 0x00000004)
T6778 8916:880.384 - 0.005ms returns 0x00
T6778 8916:880.384 JLINK_HasError()
T6778 8916:880.384 JLINK_HasError()
T6778 8916:880.384 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T6778 8916:880.384   CPU_ReadMem(4 bytes @ 0xE000ED30)
T6778 8916:881.408   Data:  02 00 00 00
T6778 8916:881.408 - 0.363ms returns 1 (0x1)
T6778 8916:881.408 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T6778 8916:881.408   CPU_ReadMem(4 bytes @ 0xE0001028)
T6778 8916:881.408   Data:  00 00 00 00
T6778 8916:881.408   Debug reg: DWT_FUNC[0]
T6778 8916:881.408 - 0.368ms returns 1 (0x1)
T6778 8916:881.408 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T6778 8916:881.408   CPU_ReadMem(4 bytes @ 0xE0001038)
T6778 8916:882.432   Data:  00 02 00 00
T6778 8916:882.432   Debug reg: DWT_FUNC[1]
T6778 8916:882.432 - 0.398ms returns 1 (0x1)
T6778 8916:882.432 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T6778 8916:882.432   CPU_ReadMem(4 bytes @ 0xE0001048)
T6778 8916:882.432   Data:  00 00 00 00
T6778 8916:882.432   Debug reg: DWT_FUNC[2]
T6778 8916:882.432 - 0.340ms returns 1 (0x1)
T6778 8916:882.432 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T6778 8916:882.432   CPU_ReadMem(4 bytes @ 0xE0001058)
T6778 8916:882.432   Data:  00 00 00 00
T6778 8916:882.432   Debug reg: DWT_FUNC[3]
T6778 8916:882.432 - 0.297ms returns 1 (0x1)
T6778 8916:882.432 JLINK_HasError()
T6778 8916:882.432 JLINK_ReadReg(R0)
T6778 8916:882.432 - 0.005ms returns 0x00000096
T6778 8916:883.456 JLINK_ReadReg(R1)
T6778 8916:883.456 - 0.004ms returns 0x00000064
T6778 8916:883.456 JLINK_ReadReg(R2)
T6778 8916:883.456 - 0.004ms returns 0x00000000
T6778 8916:883.456 JLINK_ReadReg(R3)
T6778 8916:883.456 - 0.004ms returns 0x00000000
T6778 8916:883.456 JLINK_ReadReg(R4)
T6778 8916:883.456 - 0.004ms returns 0x00000008
T6778 8916:883.456 JLINK_ReadReg(R5)
T6778 8916:883.456 - 0.004ms returns 0x000000FF
T6778 8916:883.456 JLINK_ReadReg(R6)
T6778 8916:883.456 - 0.004ms returns 0x00003AA1
T6778 8916:883.456 JLINK_ReadReg(R7)
T6778 8916:883.456 - 0.004ms returns 0x000003E8
T6778 8916:883.456 JLINK_ReadReg(R8)
T6778 8916:883.456 - 0.004ms returns 0x00000000
T6778 8916:883.456 JLINK_ReadReg(R9)
T6778 8916:883.456 - 0.004ms returns 0x00000000
T6778 8916:883.456 JLINK_ReadReg(R10)
T6778 8916:883.456 - 0.004ms returns 0x0004B698
T6778 8916:883.456 JLINK_ReadReg(R11)
T6778 8916:883.456 - 0.004ms returns 0x00000000
T6778 8916:883.456 JLINK_ReadReg(R12)
T6778 8916:883.456 - 0.004ms returns 0x00000000
T6778 8916:883.456 JLINK_ReadReg(R13 (SP))
T6778 8916:883.456 - 0.004ms returns 0x2001EC20
T6778 8916:883.456 JLINK_ReadReg(R14)
T6778 8916:883.456 - 0.004ms returns 0x0000C71B
T6778 8916:883.456 JLINK_ReadReg(R15 (PC))
T6778 8916:883.456 - 0.004ms returns 0x0000A7DA
T6778 8916:883.456 JLINK_ReadReg(XPSR)
T6778 8916:883.456 - 0.004ms returns 0x21000000
T6778 8916:883.456 JLINK_ReadReg(MSP)
T6778 8916:883.456 - 0.004ms returns 0x2001EC20
T6778 8916:883.456 JLINK_ReadReg(PSP)
T6778 8916:883.456 - 0.004ms returns 0x00000000
T6778 8916:883.456 JLINK_ReadReg(CFBP)
T6778 8916:883.456 - 0.004ms returns 0x04000000
T6778 8916:883.456 JLINK_ReadReg(FPSCR)
T6778 8916:887.552 - 4.094ms returns 0x03000010
T6778 8916:887.552 JLINK_ReadReg(FPS0)
T6778 8916:887.552 - 0.004ms returns 0x00000018
T6778 8916:887.552 JLINK_ReadReg(FPS1)
T6778 8916:887.552 - 0.004ms returns 0x41A00000
T6778 8916:887.552 JLINK_ReadReg(FPS2)
T6778 8916:887.552 - 0.004ms returns 0x41C7999A
T6778 8916:887.552 JLINK_ReadReg(FPS3)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS4)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS5)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS6)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS7)
T6778 8916:887.552 - 0.008ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS8)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS9)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS10)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS11)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS12)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS13)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS14)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS15)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS16)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS17)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS18)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS19)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS20)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS21)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS22)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS23)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS24)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS25)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS26)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS27)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS28)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS29)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS30)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T6778 8916:887.552 JLINK_ReadReg(FPS31)
T6778 8916:887.552 - 0.004ms returns 0x00000000
T2228 8916:888.576 JLINK_ReadMemEx(0x2001ED04, 0x4 Bytes, Flags = 0x02000000)
T2228 8916:888.576   CPU_ReadMem(4 bytes @ 0x2001ED04)
T2228 8916:888.576   Data:  9B 84 00 00
T2228 8916:888.576 - 0.605ms returns 4 (0x4)
T2228 8916:895.744 JLINK_ReadMemEx(0x2001ECF4, 0x4 Bytes, Flags = 0x02000000)
T2228 8916:895.744   CPU_ReadMem(4 bytes @ 0x2001ECF4)
T2228 8916:895.744   Data:  00 B8 04 00
T2228 8916:895.744 - 0.383ms returns 4 (0x4)
T2228 8916:895.744 JLINK_ReadMemEx(0x2001ECF8, 0x4 Bytes, Flags = 0x02000000)
T2228 8916:895.744   CPU_ReadMem(4 bytes @ 0x2001ECF8)
T2228 8916:895.744   Data:  D0 B8 04 00
T2228 8916:895.744 - 0.503ms returns 4 (0x4)
T2228 8916:895.744 JLINK_ReadMemEx(0x2001ECFC, 0x4 Bytes, Flags = 0x02000000)
T2228 8916:895.744   CPU_ReadMem(4 bytes @ 0x2001ECFC)
T2228 8916:895.744   Data:  00 00 00 00
T2228 8916:895.744 - 0.377ms returns 4 (0x4)
T2228 8916:895.744 JLINK_ReadMemEx(0x2001ED00, 0x4 Bytes, Flags = 0x02000000)
T2228 8916:895.744   CPU_ReadMem(4 bytes @ 0x2001ED00)
T2228 8916:896.768   Data:  00 00 00 00
T2228 8916:896.768 - 0.413ms returns 4 (0x4)
T2228 8916:897.792 JLINK_ReadMemEx(0x200030C0, 0x4 Bytes, Flags = 0x02000000)
T2228 8916:897.792   CPU_ReadMem(4 bytes @ 0x200030C0)
T2228 8916:898.816   Data:  A1 3A 00 00
T2228 8916:898.816 - 0.461ms returns 4 (0x4)
T2228 8916:898.816 JLINK_ReadMemEx(0x200030C4, 0x4 Bytes, Flags = 0x02000000)
T2228 8916:898.816   CPU_ReadMem(4 bytes @ 0x200030C4)
T2228 8916:898.816   Data:  00 00 00 00
T2228 8916:898.816 - 0.317ms returns 4 (0x4)
T2228 8916:898.816 JLINK_ReadMemEx(0x200030C8, 0x1 Bytes, Flags = 0x02000000)
T2228 8916:898.816   CPU_ReadMem(1 bytes @ 0x200030C8)
T2228 8916:899.840   Data:  00
T2228 8916:899.840 - 0.323ms returns 1 (0x1)
T2228 8916:899.840 JLINK_ReadMemEx(0x200030C9, 0x1 Bytes, Flags = 0x02000000)
T2228 8916:899.840   CPU_ReadMem(1 bytes @ 0x200030C9)
T2228 8916:899.840   Data:  01
T2228 8916:899.840 - 0.294ms returns 1 (0x1)
T2228 8916:899.840 JLINK_ReadMemEx(0x200030CA, 0x2 Bytes, Flags = 0x02000000)
T2228 8916:899.840   CPU_ReadMem(2 bytes @ 0x200030CA)
T2228 8916:899.840   Data:  64 00
T2228 8916:899.840 - 0.340ms returns 2 (0x2)
T2228 8916:899.840 JLINK_ReadMemEx(0x2001ECF0, 0x2 Bytes, Flags = 0x02000000)
T2228 8916:899.840   CPU_ReadMem(2 bytes @ 0x2001ECF0)
T2228 8916:899.840   Data:  04 00
T2228 8916:899.840 - 0.289ms returns 2 (0x2)
T2228 8916:899.840 JLINK_ReadMemEx(0x2001ECEC, 0x4 Bytes, Flags = 0x02000000)
T2228 8916:899.840   CPU_ReadMem(4 bytes @ 0x2001ECEC)
T2228 8916:899.840   Data:  00 00 00 00
T2228 8916:899.840 - 0.284ms returns 4 (0x4)
T2228 8916:899.840 JLINK_ReadMemEx(0x2001ECE4, 0x2 Bytes, Flags = 0x02000000)
T2228 8916:899.840   CPU_ReadMem(2 bytes @ 0x2001ECE4)
T2228 8916:900.864   Data:  04 00
T2228 8916:900.864 - 0.412ms returns 2 (0x2)
T2228 8916:900.864 JLINK_ReadMemEx(0x2001ECE0, 0x2 Bytes, Flags = 0x02000000)
T2228 8916:900.864   CPU_ReadMem(2 bytes @ 0x2001ECE0)
T2228 8916:900.864   Data:  DC 00
T2228 8916:900.864 - 0.330ms returns 2 (0x2)
T2228 8916:900.864 JLINK_ReadMemEx(0x2001ECC8, 0x1 Bytes, Flags = 0x02000000)
T2228 8916:900.864   CPU_ReadMem(1 bytes @ 0x2001ECC8)
T2228 8916:901.888   Data:  00
T2228 8916:901.888 - 0.283ms returns 1 (0x1)
T2228 8916:901.888 JLINK_ReadMemEx(0x2001ECC4, 0x4 Bytes, Flags = 0x02000000)
T2228 8916:901.888   CPU_ReadMem(4 bytes @ 0x2001ECC4)
T2228 8916:901.888   Data:  02 00 00 00
T2228 8916:901.888 - 0.364ms returns 4 (0x4)
T2228 8916:901.888 JLINK_ReadMemEx(0x2001ECC0, 0x4 Bytes, Flags = 0x02000000)
T2228 8916:901.888   CPU_ReadMem(4 bytes @ 0x2001ECC0)
T2228 8916:901.888   Data:  00 00 00 00
T2228 8916:901.888 - 0.273ms returns 4 (0x4)
T2228 8916:901.888 JLINK_ReadMemEx(0x2001ECBC, 0x1 Bytes, Flags = 0x02000000)
T2228 8916:901.888   CPU_ReadMem(1 bytes @ 0x2001ECBC)
T2228 8916:902.912   Data:  00
T2228 8916:902.912 - 0.346ms returns 1 (0x1)
T2228 8916:902.912 JLINK_ReadMemEx(0x2001ECB8, 0x1 Bytes, Flags = 0x02000000)
T2228 8916:902.912   CPU_ReadMem(1 bytes @ 0x2001ECB8)
T2228 8916:902.912   Data:  00
T2228 8916:902.912 - 0.316ms returns 1 (0x1)
T2228 8916:902.912 JLINK_ReadMemEx(0x2001ECB4, 0x1 Bytes, Flags = 0x02000000)
T2228 8916:902.912   CPU_ReadMem(1 bytes @ 0x2001ECB4)
T2228 8916:903.936   Data:  01
T2228 8916:903.936 - 0.391ms returns 1 (0x1)
T2228 8916:903.936 JLINK_ReadMemEx(0x2001EC94, 0x2 Bytes, Flags = 0x02000000)
T2228 8916:903.936   CPU_ReadMem(2 bytes @ 0x2001EC94)
T2228 8916:903.936   Data:  1C 02
T2228 8916:903.936 - 0.302ms returns 2 (0x2)
T2228 8916:903.936 JLINK_ReadMemEx(0x2001EC90, 0x2 Bytes, Flags = 0x02000000)
T2228 8916:903.936   CPU_ReadMem(2 bytes @ 0x2001EC90)
T2228 8916:903.936   Data:  1C 02
T2228 8916:903.936 - 0.362ms returns 2 (0x2)
T2228 8916:903.936 JLINK_ReadMemEx(0x2001EC8C, 0x2 Bytes, Flags = 0x02000000)
T2228 8916:903.936   CPU_ReadMem(2 bytes @ 0x2001EC8C)
T2228 8916:903.936   Data:  00 00
T2228 8916:903.936 - 0.312ms returns 2 (0x2)
T2228 8916:903.936 JLINK_ReadMemEx(0x2001EC88, 0x2 Bytes, Flags = 0x02000000)
T2228 8916:903.936   CPU_ReadMem(2 bytes @ 0x2001EC88)
T2228 8916:903.936   Data:  26 02
T2228 8916:903.936 - 0.379ms returns 2 (0x2)
T2228 8916:903.936 JLINK_ReadMemEx(0x2001EC84, 0x4 Bytes, Flags = 0x02000000)
T2228 8916:903.936   CPU_ReadMem(4 bytes @ 0x2001EC84)
T2228 8916:904.960   Data:  32 00 00 00
T2228 8916:904.960 - 0.312ms returns 4 (0x4)
T2228 8916:904.960 JLINK_ReadMemEx(0x2001EC70, 0x2 Bytes, Flags = 0x02000000)
T2228 8916:904.960   CPU_ReadMem(2 bytes @ 0x2001EC70)
T2228 8916:904.960   Data:  E9 07
T2228 8916:904.960 - 0.351ms returns 2 (0x2)
T2228 8916:904.960 JLINK_ReadMemEx(0x2001EC6C, 0x2 Bytes, Flags = 0x02000000)
T2228 8916:904.960   CPU_ReadMem(2 bytes @ 0x2001EC6C)
T2228 8916:905.984   Data:  08 00
T2228 8916:905.984 - 0.292ms returns 2 (0x2)
T2228 8916:905.984 JLINK_ReadMemEx(0x2001EC68, 0x2 Bytes, Flags = 0x02000000)
T2228 8916:905.984   CPU_ReadMem(2 bytes @ 0x2001EC68)
T2228 8916:905.984   Data:  01 00
T2228 8916:905.984 - 0.368ms returns 2 (0x2)
T2228 8916:905.984 JLINK_ReadMemEx(0x2001EC64, 0x2 Bytes, Flags = 0x02000000)
T2228 8916:905.984   CPU_ReadMem(2 bytes @ 0x2001EC64)
T2228 8916:907.008   Data:  10 00
T2228 8916:907.008 - 0.373ms returns 2 (0x2)
T2228 8916:907.008 JLINK_ReadMemEx(0x2001EC60, 0x2 Bytes, Flags = 0x02000000)
T2228 8916:907.008   CPU_ReadMem(2 bytes @ 0x2001EC60)
T2228 8916:907.008   Data:  2A 00
T2228 8916:907.008 - 0.323ms returns 2 (0x2)
T2228 8916:907.008 JLINK_ReadMemEx(0x2001EC5C, 0x2 Bytes, Flags = 0x02000000)
T2228 8916:907.008   CPU_ReadMem(2 bytes @ 0x2001EC5C)
T2228 8916:907.008   Data:  20 00
T2228 8916:907.008 - 0.425ms returns 2 (0x2)
T2228 8916:907.008 JLINK_ReadMemEx(0x2001EC2C, 0x2 Bytes, Flags = 0x02000000)
T2228 8916:908.032   CPU_ReadMem(2 bytes @ 0x2001EC2C)
T2228 8916:908.032   Data:  00 00
T2228 8916:908.032 - 0.380ms returns 2 (0x2)
T2228 8916:908.032 JLINK_ReadMemEx(0x2001EC28, 0x2 Bytes, Flags = 0x02000000)
T2228 8916:908.032   CPU_ReadMem(2 bytes @ 0x2001EC28)
T2228 8916:908.032   Data:  96 00
T2228 8916:908.032 - 0.385ms returns 2 (0x2)
T2228 8916:908.032 JLINK_ReadMemEx(0x20004938, 0x10 Bytes, Flags = 0x02000000)
T2228 8916:908.032   CPU_ReadMem(16 bytes @ 0x20004938)
T2228 8916:908.032   Data:  43 56 31 2E 30 2E 31 00 00 00 00 00 00 00 00 00
T2228 8916:908.032 - 0.455ms returns 16 (0x10)
T2228 8916:909.056 JLINK_ReadMemEx(0x2001EC4C, 0x10 Bytes, Flags = 0x02000000)
T2228 8916:909.056   CPU_ReadMem(16 bytes @ 0x2001EC4C)
T2228 8916:909.056   Data:  43 56 31 2E 30 2E 31 00 00 00 00 00 00 00 00 00
T2228 8916:909.056 - 0.459ms returns 16 (0x10)
T2228 8916:909.056 JLINK_ReadMemEx(0x2001EC44, 0x8 Bytes, Flags = 0x02000000)
T2228 8916:909.056   CPU_ReadMem(8 bytes @ 0x2001EC44)
T2228 8916:910.080   Data:  53 00 20 13 54 51 35 00
T2228 8916:910.080 - 0.366ms returns 8 (0x8)
T2228 8916:910.080 JLINK_ReadMemEx(0x2001EC30, 0x11 Bytes, Flags = 0x02000000)
T2228 8916:910.080   CPU_ReadMem(17 bytes @ 0x2001EC30)
T2228 8916:910.080   Data:  35 33 30 30 32 30 31 33 35 34 35 31 33 35 30 30 ...
T2228 8916:910.080 - 0.518ms returns 17 (0x11)
T2228 8916:910.080 JLINK_ReadMemEx(0x00000000, 0x1 Bytes, Flags = 0x02000000)
T2228 8916:910.080   CPU_ReadMem(64 bytes @ 0x00000000)
T2228 8916:911.104    -- Updating DA cache (64 bytes @ 0x00000000)
T2228 8916:911.104    -- Read from DA cache (1 bytes @ 0x00000000)
T2228 8916:911.104   Data:  50
T2228 8916:911.104 - 0.756ms returns 1 (0x1)
T2228 8916:911.104 JLINK_ReadMemEx(0x00000000, 0x2 Bytes, Flags = 0x02000000)
T2228 8916:911.104    -- Read from DA cache (2 bytes @ 0x00000000)
T2228 8916:911.104   Data:  50 17
T2228 8916:911.104 - 0.018ms returns 2 (0x2)
T2228 8916:916.224 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8916:916.224   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8916:916.224   Data:  00 08 00 20
T2228 8916:916.224 - 0.472ms returns 4 (0x4)
T2228 8916:919.296 JLINK_ReadMemEx(0x0000A7DA, 0x2 Bytes, Flags = 0x02000000)
T2228 8916:919.296   CPU_ReadMem(64 bytes @ 0x0000A7C0)
T2228 8916:920.320    -- Updating DA cache (64 bytes @ 0x0000A7C0)
T2228 8916:920.320    -- Read from DA cache (2 bytes @ 0x0000A7DA)
T2228 8916:920.320   Data:  BD F8
T2228 8916:920.320 - 0.743ms returns 2 (0x2)
T2228 8916:920.320 JLINK_ReadMemEx(0x0000A7DC, 0x24 Bytes, Flags = 0x02000000)
T2228 8916:920.320    -- Read from DA cache (36 bytes @ 0x0000A7DC)
T2228 8916:920.320   Data:  08 00 D6 49 08 80 BD F8 08 00 00 EB 80 00 4F F6 ...
T2228 8916:920.320 - 0.019ms returns 36 (0x24)
T2228 8916:920.320 JLINK_ReadMemEx(0x0000A800, 0x18 Bytes, Flags = 0x02000000)
T2228 8916:920.320   CPU_ReadMem(64 bytes @ 0x0000A800)
T2228 8916:920.320    -- Updating DA cache (64 bytes @ 0x0000A800)
T2228 8916:920.320    -- Read from DA cache (24 bytes @ 0x0000A800)
T2228 8916:920.320   Data:  03 A9 42 F2 E9 40 02 F0 CE FB BD F8 08 00 96 30 ...
T2228 8916:920.320 - 0.689ms returns 24 (0x18)
T6778 8918:632.448 JLINK_ReadMemEx(0x0000A7DA, 0x2 Bytes, Flags = 0x02000000)
T6778 8918:632.448    -- Read from DA cache (2 bytes @ 0x0000A7DA)
T6778 8918:632.448   Data:  BD F8
T6778 8918:632.448 - 0.070ms returns 2 (0x2)
T6778 8918:632.448 JLINK_HasError()
T6778 8918:632.448 JLINK_Step()
T6778 8918:632.448    -- Read from DA cache (2 bytes @ 0x0000A7DA)
T6778 8918:632.448   CPU_ReadMem(4 bytes @ 0xE000ED18)
T6778 8918:632.448   CPU_WriteMem(4 bytes @ 0xE000ED18)
T6778 8918:633.472   CPU_ReadMem(4 bytes @ 0xE000ED18)
T6778 8918:633.472   CPU_WriteMem(4 bytes @ 0xE000ED18)
T6778 8918:641.664   -- Not simulated
T6778 8918:641.664   CPU_WriteMem(4 bytes @ 0xE0002008)
T6778 8918:644.736 - 12.902ms returns 0
T6778 8918:644.736 JLINK_HasError()
T6778 8918:644.736 JLINK_ReadReg(R15 (PC))
T6778 8918:644.736 - 0.024ms returns 0x0000A7DE
T6778 8918:644.736 JLINK_ReadReg(XPSR)
T6778 8918:644.736 - 0.005ms returns 0x21000000
T6778 8918:645.760 JLINK_HasError()
T6778 8918:645.760 JLINK_SetBPEx(Addr = 0x0000A7DA, Type = 0xFFFFFFF2)
T6778 8918:645.760 - 0.009ms returns 0x00000005
T6778 8918:645.760 JLINK_HasError()
T6778 8918:645.760 JLINK_HasError()
T6778 8918:645.760 JLINK_Go()
T6778 8918:645.760   CPU_WriteMem(4 bytes @ 0x********)
T6778 8918:646.784   CPU_ReadMem(4 bytes @ 0x********)
T6778 8918:646.784   CPU_WriteMem(4 bytes @ 0xE0002008)
T6778 8918:647.808 - 2.131ms
T6778 8918:748.160 JLINK_HasError()
T6778 8918:748.160 JLINK_IsHalted()
T6778 8918:748.160 - 0.351ms returns FALSE
T6778 8918:849.536 JLINK_HasError()
T6778 8918:849.536 JLINK_IsHalted()
T6778 8918:849.536 - 0.403ms returns FALSE
T6778 8918:949.888 JLINK_HasError()
T6778 8918:949.888 JLINK_IsHalted()
T6778 8918:949.888 - 0.520ms returns FALSE
T6778 8919:051.264 JLINK_HasError()
T6778 8919:051.264 JLINK_IsHalted()
T6778 8919:051.264 - 0.401ms returns FALSE
T6778 8919:152.640 JLINK_HasError()
T6778 8919:152.640 JLINK_IsHalted()
T6778 8919:152.640 - 0.417ms returns FALSE
T6778 8919:254.016 JLINK_HasError()
T6778 8919:254.016 JLINK_HasError()
T6778 8919:254.016 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8919:254.016   CPU_ReadMem(4 bytes @ 0xE0001004)
T6778 8919:254.016   Data:  4F 60 4B BA
T6778 8919:254.016   Debug reg: DWT_CYCCNT
T6778 8919:254.016 - 0.385ms returns 1 (0x1)
T2228 8919:256.064 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8919:256.064   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8919:256.064   Data:  00 08 00 20
T2228 8919:256.064 - 0.474ms returns 4 (0x4)
T2228 8919:256.064 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8919:256.064   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8919:257.088   Data:  00 08 00 20
T2228 8919:257.088 - 0.332ms returns 4 (0x4)
T6778 8919:259.136 JLINK_IsHalted()
T6778 8919:260.160 - 0.518ms returns FALSE
T6778 8919:361.536 JLINK_HasError()
T6778 8919:361.536 JLINK_IsHalted()
T6778 8919:361.536 - 0.326ms returns FALSE
T6778 8919:462.912 JLINK_HasError()
T6778 8919:462.912 JLINK_IsHalted()
T6778 8919:462.912 - 0.316ms returns FALSE
T6778 8919:564.288 JLINK_HasError()
T6778 8919:564.288 JLINK_IsHalted()
T6778 8919:564.288 - 0.395ms returns FALSE
T6778 8919:664.640 JLINK_HasError()
T6778 8919:664.640 JLINK_IsHalted()
T6778 8919:665.664 - 0.454ms returns FALSE
T6778 8919:767.040 JLINK_HasError()
T6778 8919:767.040 JLINK_IsHalted()
T6778 8919:767.040 - 0.318ms returns FALSE
T6778 8919:867.392 JLINK_HasError()
T6778 8919:867.392 JLINK_HasError()
T6778 8919:867.392 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8919:867.392   Data:  4F 60 4B BA
T6778 8919:867.392   Debug reg: DWT_CYCCNT
T6778 8919:867.392 - 0.031ms returns 1 (0x1)
T2228 8919:872.512 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8919:872.512   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8919:872.512   Data:  00 08 00 20
T2228 8919:872.512 - 0.502ms returns 4 (0x4)
T2228 8919:873.536 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8919:873.536   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8919:873.536   Data:  00 08 00 20
T2228 8919:873.536 - 0.476ms returns 4 (0x4)
T6778 8919:876.608 JLINK_IsHalted()
T6778 8919:876.608 - 0.530ms returns FALSE
T6778 8919:977.984 JLINK_HasError()
T6778 8919:977.984 JLINK_IsHalted()
T6778 8919:977.984 - 0.360ms returns FALSE
T6778 8920:078.336 JLINK_HasError()
T6778 8920:078.336 JLINK_IsHalted()
T6778 8920:078.336 - 0.318ms returns FALSE
T6778 8920:179.712 JLINK_HasError()
T6778 8920:188.928 JLINK_IsHalted()
T6778 8920:188.928 - 0.341ms returns FALSE
T6778 8920:290.304 JLINK_HasError()
T6778 8920:290.304 JLINK_IsHalted()
T6778 8920:290.304 - 0.348ms returns FALSE
T6778 8920:390.656 JLINK_HasError()
T6778 8920:390.656 JLINK_HasError()
T6778 8920:390.656 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8920:390.656   Data:  4F 60 4B BA
T6778 8920:390.656   Debug reg: DWT_CYCCNT
T6778 8920:390.656 - 0.031ms returns 1 (0x1)
T2228 8920:392.704 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8920:393.728   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8920:393.728   Data:  00 08 00 20
T2228 8920:393.728 - 0.597ms returns 4 (0x4)
T2228 8920:393.728 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8920:393.728   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8920:393.728   Data:  00 08 00 20
T2228 8920:393.728 - 0.342ms returns 4 (0x4)
T6778 8920:398.848 JLINK_IsHalted()
T6778 8920:398.848 - 0.346ms returns FALSE
T6778 8920:500.224 JLINK_HasError()
T6778 8920:500.224 JLINK_IsHalted()
T6778 8920:500.224 - 0.343ms returns FALSE
T6778 8920:600.576 JLINK_HasError()
T6778 8920:600.576 JLINK_IsHalted()
T6778 8920:601.600 - 0.409ms returns FALSE
T6778 8920:701.952 JLINK_HasError()
T6778 8920:701.952 JLINK_IsHalted()
T6778 8920:702.976 - 0.310ms returns FALSE
T6778 8920:803.328 JLINK_HasError()
T6778 8920:803.328 JLINK_IsHalted()
T6778 8920:804.352 - 0.436ms returns FALSE
T6778 8920:904.704 JLINK_HasError()
T6778 8920:904.704 JLINK_HasError()
T6778 8920:904.704 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8920:904.704   Data:  4F 60 4B BA
T6778 8920:904.704   Debug reg: DWT_CYCCNT
T6778 8920:904.704 - 0.186ms returns 1 (0x1)
T2228 8920:910.848 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8920:910.848   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8920:911.872   Data:  00 08 00 20
T2228 8920:911.872 - 0.710ms returns 4 (0x4)
T2228 8920:911.872 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8920:911.872   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8920:912.896   Data:  00 08 00 20
T2228 8920:912.896 - 1.071ms returns 4 (0x4)
T6778 8920:915.968 JLINK_IsHalted()
T6778 8920:916.992 - 0.666ms returns FALSE
T6778 8921:016.320 JLINK_HasError()
T6778 8921:016.320 JLINK_IsHalted()
T6778 8921:017.344 - 0.370ms returns FALSE
T6778 8921:118.720 JLINK_HasError()
T6778 8921:118.720 JLINK_IsHalted()
T6778 8921:118.720 - 0.411ms returns FALSE
T6778 8921:219.072 JLINK_HasError()
T6778 8921:219.072 JLINK_IsHalted()
T6778 8921:220.096 - 0.479ms returns FALSE
T6778 8921:320.448 JLINK_HasError()
T6778 8921:320.448 JLINK_IsHalted()
T6778 8921:320.448 - 0.401ms returns FALSE
T6778 8921:421.824 JLINK_HasError()
T6778 8921:421.824 JLINK_HasError()
T6778 8921:421.824 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8921:421.824   Data:  4F 60 4B BA
T6778 8921:421.824   Debug reg: DWT_CYCCNT
T6778 8921:421.824 - 0.038ms returns 1 (0x1)
T2228 8921:423.872 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8921:423.872   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8921:424.896   Data:  00 08 00 20
T2228 8921:424.896 - 0.427ms returns 4 (0x4)
T2228 8921:424.896 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8921:424.896   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8921:424.896   Data:  00 08 00 20
T2228 8921:424.896 - 0.321ms returns 4 (0x4)
T6778 8921:428.992 JLINK_IsHalted()
T6778 8921:428.992 - 0.345ms returns FALSE
T6778 8921:528.320 JLINK_HasError()
T6778 8921:528.320 JLINK_IsHalted()
T6778 8921:529.344 - 0.307ms returns FALSE
T6778 8921:629.696 JLINK_HasError()
T6778 8921:629.696 JLINK_IsHalted()
T6778 8921:630.720 - 0.549ms returns FALSE
T6778 8921:731.072 JLINK_HasError()
T6778 8921:731.072 JLINK_IsHalted()
T6778 8921:732.096 - 0.393ms returns FALSE
T6778 8921:831.424 JLINK_HasError()
T6778 8921:831.424 JLINK_IsHalted()
T6778 8921:831.424 - 0.407ms returns FALSE
T6778 8921:932.800 JLINK_HasError()
T6778 8921:932.800 JLINK_HasError()
T6778 8921:932.800 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8921:932.800   Data:  4F 60 4B BA
T6778 8921:932.800   Debug reg: DWT_CYCCNT
T6778 8921:932.800 - 0.042ms returns 1 (0x1)
T2228 8921:934.848 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8921:935.872   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8921:935.872   Data:  00 08 00 20
T2228 8921:935.872 - 0.729ms returns 4 (0x4)
T2228 8921:936.896 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8921:936.896   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8921:936.896   Data:  00 08 00 20
T2228 8921:936.896 - 0.447ms returns 4 (0x4)
T6778 8921:939.968 JLINK_IsHalted()
T6778 8921:940.992 - 0.422ms returns FALSE
T6778 8922:040.320 JLINK_HasError()
T6778 8922:040.320 JLINK_IsHalted()
T6778 8922:040.320 - 0.313ms returns FALSE
T6778 8922:142.720 JLINK_HasError()
T6778 8922:142.720 JLINK_IsHalted()
T6778 8922:142.720 - 0.375ms returns FALSE
T6778 8922:243.072 JLINK_HasError()
T6778 8922:243.072 JLINK_IsHalted()
T6778 8922:243.072 - 0.381ms returns FALSE
T6778 8922:343.424 JLINK_HasError()
T6778 8922:343.424 JLINK_IsHalted()
T6778 8922:343.424 - 0.496ms returns FALSE
T6778 8922:444.800 JLINK_HasError()
T6778 8922:444.800 JLINK_HasError()
T6778 8922:444.800 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8922:444.800   Data:  4F 60 4B BA
T6778 8922:444.800   Debug reg: DWT_CYCCNT
T6778 8922:444.800 - 0.034ms returns 1 (0x1)
T2228 8922:445.824 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8922:445.824   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8922:446.848   Data:  00 08 00 20
T2228 8922:446.848 - 0.573ms returns 4 (0x4)
T2228 8922:446.848 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8922:446.848   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8922:447.872   Data:  00 08 00 20
T2228 8922:447.872 - 0.480ms returns 4 (0x4)
T6778 8922:449.920 JLINK_IsHalted()
T6778 8922:450.944 - 0.620ms returns FALSE
T6778 8922:552.320 JLINK_HasError()
T6778 8922:552.320 JLINK_IsHalted()
T6778 8922:552.320 - 0.368ms returns FALSE
T6778 8922:653.696 JLINK_HasError()
T6778 8922:653.696 JLINK_IsHalted()
T6778 8922:653.696 - 0.319ms returns FALSE
T6778 8922:754.048 JLINK_HasError()
T6778 8922:754.048 JLINK_IsHalted()
T6778 8922:755.072 - 0.364ms returns FALSE
T6778 8922:855.424 JLINK_HasError()
T6778 8922:855.424 JLINK_IsHalted()
T6778 8922:855.424 - 0.559ms returns FALSE
T6778 8922:955.776 JLINK_HasError()
T6778 8922:955.776 JLINK_HasError()
T6778 8922:955.776 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8922:955.776   Data:  4F 60 4B BA
T6778 8922:955.776   Debug reg: DWT_CYCCNT
T6778 8922:955.776 - 0.027ms returns 1 (0x1)
T2228 8922:957.824 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8922:957.824   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8922:957.824   Data:  00 08 00 20
T2228 8922:957.824 - 0.475ms returns 4 (0x4)
T2228 8922:957.824 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8922:957.824   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8922:958.848   Data:  00 08 00 20
T2228 8922:958.848 - 0.437ms returns 4 (0x4)
T6778 8922:962.944 JLINK_IsHalted()
T6778 8922:962.944 - 0.362ms returns FALSE
T6778 8923:064.320 JLINK_HasError()
T6778 8923:064.320 JLINK_IsHalted()
T6778 8923:064.320 - 0.345ms returns FALSE
T6778 8923:164.672 JLINK_HasError()
T6778 8923:164.672 JLINK_IsHalted()
T6778 8923:165.696 - 0.409ms returns FALSE
T6778 8923:266.048 JLINK_HasError()
T6778 8923:266.048 JLINK_IsHalted()
T6778 8923:267.072 - 0.467ms returns FALSE
T6778 8923:367.424 JLINK_HasError()
T6778 8923:367.424 JLINK_IsHalted()
T6778 8923:367.424 - 0.354ms returns FALSE
T6778 8923:468.800 JLINK_HasError()
T6778 8923:468.800 JLINK_IsHalted()
T6778 8923:469.824 - 0.533ms returns FALSE
T6778 8923:569.152 JLINK_HasError()
T6778 8923:569.152 JLINK_HasError()
T6778 8923:569.152 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8923:570.176   Data:  4F 60 4B BA
T6778 8923:570.176   Debug reg: DWT_CYCCNT
T6778 8923:570.176 - 0.043ms returns 1 (0x1)
T2228 8923:576.320 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8923:576.320   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8923:577.344   Data:  00 08 00 20
T2228 8923:577.344 - 0.504ms returns 4 (0x4)
T2228 8923:577.344 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8923:577.344   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8923:578.368   Data:  00 08 00 20
T2228 8923:578.368 - 0.448ms returns 4 (0x4)
T6778 8923:585.536 JLINK_IsHalted()
T6778 8923:586.560 - 0.424ms returns FALSE
T6778 8923:686.912 JLINK_HasError()
T6778 8923:686.912 JLINK_IsHalted()
T6778 8923:686.912 - 0.413ms returns FALSE
T6778 8923:788.288 JLINK_HasError()
T6778 8923:788.288 JLINK_IsHalted()
T6778 8923:788.288 - 0.309ms returns FALSE
T6778 8923:888.640 JLINK_HasError()
T6778 8923:888.640 JLINK_IsHalted()
T6778 8923:888.640 - 0.469ms returns FALSE
T6778 8923:990.016 JLINK_HasError()
T6778 8923:990.016 JLINK_IsHalted()
T6778 8923:990.016 - 0.472ms returns FALSE
T6778 8924:091.392 JLINK_HasError()
T6778 8924:091.392 JLINK_HasError()
T6778 8924:091.392 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8924:091.392   Data:  4F 60 4B BA
T6778 8924:091.392   Debug reg: DWT_CYCCNT
T6778 8924:091.392 - 0.038ms returns 1 (0x1)
T2228 8924:092.416 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8924:092.416   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8924:093.440   Data:  00 08 00 20
T2228 8924:093.440 - 0.401ms returns 4 (0x4)
T2228 8924:093.440 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8924:093.440   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8924:093.440   Data:  00 08 00 20
T2228 8924:093.440 - 0.357ms returns 4 (0x4)
T6778 8924:098.560 JLINK_IsHalted()
T6778 8924:099.584 - 0.327ms returns FALSE
T6778 8924:198.912 JLINK_HasError()
T6778 8924:198.912 JLINK_IsHalted()
T6778 8924:199.936 - 0.407ms returns FALSE
T6778 8924:300.288 JLINK_HasError()
T6778 8924:300.288 JLINK_IsHalted()
T6778 8924:301.312 - 0.315ms returns FALSE
T6778 8924:400.640 JLINK_HasError()
T6778 8924:400.640 JLINK_IsHalted()
T6778 8924:401.664 - 0.510ms returns FALSE
T6778 8924:502.016 JLINK_HasError()
T6778 8924:502.016 JLINK_IsHalted()
T6778 8924:502.016 - 0.481ms returns FALSE
T6778 8924:604.416 JLINK_HasError()
T6778 8924:604.416 JLINK_HasError()
T6778 8924:604.416 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8924:604.416   Data:  4F 60 4B BA
T6778 8924:604.416   Debug reg: DWT_CYCCNT
T6778 8924:604.416 - 0.027ms returns 1 (0x1)
T2228 8924:605.440 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8924:605.440   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8924:606.464   Data:  00 08 00 20
T2228 8924:606.464 - 0.405ms returns 4 (0x4)
T2228 8924:606.464 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8924:606.464   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8924:606.464   Data:  00 08 00 20
T2228 8924:606.464 - 0.393ms returns 4 (0x4)
T6778 8924:609.536 JLINK_IsHalted()
T6778 8924:609.536 - 0.325ms returns FALSE
T6778 8924:710.912 JLINK_HasError()
T6778 8924:710.912 JLINK_IsHalted()
T6778 8924:710.912 - 0.490ms returns FALSE
T6778 8924:811.264 JLINK_HasError()
T6778 8924:811.264 JLINK_IsHalted()
T6778 8924:811.264 - 0.429ms returns FALSE
T6778 8924:911.616 JLINK_HasError()
T6778 8924:911.616 JLINK_IsHalted()
T6778 8924:912.640 - 0.329ms returns FALSE
T6778 8925:014.016 JLINK_HasError()
T6778 8925:014.016 JLINK_IsHalted()
T6778 8925:014.016 - 0.467ms returns FALSE
T6778 8925:115.392 JLINK_HasError()
T6778 8925:115.392 JLINK_HasError()
T6778 8925:115.392 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8925:115.392   Data:  4F 60 4B BA
T6778 8925:115.392   Debug reg: DWT_CYCCNT
T6778 8925:115.392 - 0.028ms returns 1 (0x1)
T2228 8925:116.416 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8925:116.416   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8925:117.440   Data:  00 08 00 20
T2228 8925:117.440 - 0.447ms returns 4 (0x4)
T2228 8925:117.440 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8925:117.440   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8925:117.440   Data:  00 08 00 20
T2228 8925:117.440 - 0.428ms returns 4 (0x4)
T6778 8925:120.512 JLINK_IsHalted()
T6778 8925:120.512 - 0.307ms returns FALSE
T6778 8925:221.888 JLINK_HasError()
T6778 8925:221.888 JLINK_IsHalted()
T6778 8925:222.912 - 0.331ms returns FALSE
T6778 8925:322.240 JLINK_HasError()
T6778 8925:322.240 JLINK_IsHalted()
T6778 8925:323.264 - 0.310ms returns FALSE
T6778 8925:423.616 JLINK_HasError()
T6778 8925:423.616 JLINK_IsHalted()
T6778 8925:424.640 - 0.399ms returns FALSE
T6778 8925:524.992 JLINK_HasError()
T6778 8925:524.992 JLINK_IsHalted()
T6778 8925:526.016 - 0.505ms returns FALSE
T6778 8925:626.368 JLINK_HasError()
T6778 8925:629.440 JLINK_HasError()
T6778 8925:629.440 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8925:629.440   Data:  4F 60 4B BA
T6778 8925:629.440   Debug reg: DWT_CYCCNT
T6778 8925:629.440 - 0.030ms returns 1 (0x1)
T2228 8925:638.656 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8925:638.656   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8925:639.680   Data:  00 08 00 20
T2228 8925:639.680 - 0.418ms returns 4 (0x4)
T2228 8925:639.680 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8925:639.680   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8925:640.704   Data:  00 08 00 20
T2228 8925:640.704 - 0.394ms returns 4 (0x4)
T6778 8925:646.848 JLINK_IsHalted()
T6778 8925:647.872 - 0.365ms returns FALSE
T6778 8925:747.200 JLINK_HasError()
T6778 8925:747.200 JLINK_IsHalted()
T6778 8925:748.224 - 0.756ms returns FALSE
T6778 8925:849.600 JLINK_HasError()
T6778 8925:849.600 JLINK_IsHalted()
T6778 8925:849.600 - 0.516ms returns FALSE
T6778 8925:950.976 JLINK_HasError()
T6778 8925:950.976 JLINK_IsHalted()
T6778 8925:950.976 - 0.411ms returns FALSE
T6778 8926:052.352 JLINK_HasError()
T6778 8926:052.352 JLINK_IsHalted()
T6778 8926:052.352 - 0.409ms returns FALSE
T6778 8926:152.704 JLINK_HasError()
T6778 8926:152.704 JLINK_HasError()
T6778 8926:152.704 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8926:152.704   Data:  4F 60 4B BA
T6778 8926:152.704   Debug reg: DWT_CYCCNT
T6778 8926:152.704 - 0.037ms returns 1 (0x1)
T2228 8926:156.800 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8926:156.800   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8926:156.800   Data:  00 08 00 20
T2228 8926:156.800 - 0.495ms returns 4 (0x4)
T2228 8926:156.800 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8926:156.800   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8926:156.800   Data:  00 08 00 20
T2228 8926:156.800 - 0.346ms returns 4 (0x4)
T6778 8926:160.896 JLINK_IsHalted()
T6778 8926:160.896 - 0.350ms returns FALSE
T6778 8926:261.248 JLINK_HasError()
T6778 8926:262.272 JLINK_IsHalted()
T6778 8926:262.272 - 0.317ms returns FALSE
T6778 8926:362.624 JLINK_HasError()
T6778 8926:362.624 JLINK_IsHalted()
T6778 8926:362.624 - 0.306ms returns FALSE
T6778 8926:464.000 JLINK_HasError()
T6778 8926:464.000 JLINK_IsHalted()
T6778 8926:464.000 - 0.402ms returns FALSE
T6778 8926:565.376 JLINK_HasError()
T6778 8926:565.376 JLINK_IsHalted()
T6778 8926:566.400 - 0.327ms returns FALSE
T6778 8926:666.752 JLINK_HasError()
T6778 8926:666.752 JLINK_HasError()
T6778 8926:666.752 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8926:666.752   Data:  4F 60 4B BA
T6778 8926:666.752   Debug reg: DWT_CYCCNT
T6778 8926:666.752 - 0.027ms returns 1 (0x1)
T2228 8926:668.800 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8926:668.800   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8926:668.800   Data:  00 08 00 20
T2228 8926:668.800 - 0.489ms returns 4 (0x4)
T2228 8926:668.800 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8926:668.800   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8926:668.800   Data:  00 08 00 20
T2228 8926:668.800 - 0.298ms returns 4 (0x4)
T6778 8926:672.896 JLINK_IsHalted()
T6778 8926:672.896 - 0.322ms returns FALSE
T6778 8926:774.272 JLINK_HasError()
T6778 8926:774.272 JLINK_IsHalted()
T6778 8926:774.272 - 0.356ms returns FALSE
T6778 8926:874.624 JLINK_HasError()
T6778 8926:874.624 JLINK_IsHalted()
T6778 8926:875.648 - 0.624ms returns FALSE
T6778 8926:976.000 JLINK_HasError()
T6778 8926:976.000 JLINK_IsHalted()
T6778 8926:976.000 - 0.477ms returns FALSE
T6778 8927:077.376 JLINK_HasError()
T6778 8927:077.376 JLINK_IsHalted()
T6778 8927:078.400 - 0.504ms returns FALSE
T6778 8927:178.752 JLINK_HasError()
T6778 8927:178.752 JLINK_HasError()
T6778 8927:178.752 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8927:178.752   Data:  4F 60 4B BA
T6778 8927:178.752   Debug reg: DWT_CYCCNT
T6778 8927:178.752 - 0.036ms returns 1 (0x1)
T2228 8927:180.800 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8927:180.800   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8927:180.800   Data:  00 08 00 20
T2228 8927:180.800 - 0.469ms returns 4 (0x4)
T2228 8927:180.800 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8927:180.800   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8927:181.824   Data:  00 08 00 20
T2228 8927:181.824 - 0.451ms returns 4 (0x4)
T6778 8927:184.896 JLINK_IsHalted()
T6778 8927:184.896 - 0.319ms returns FALSE
T6778 8927:285.248 JLINK_HasError()
T6778 8927:285.248 JLINK_IsHalted()
T6778 8927:286.272 - 0.341ms returns FALSE
T6778 8927:385.600 JLINK_HasError()
T6778 8927:385.600 JLINK_IsHalted()
T6778 8927:386.624 - 0.473ms returns FALSE
T6778 8927:486.976 JLINK_HasError()
T6778 8927:486.976 JLINK_IsHalted()
T6778 8927:488.000 - 0.473ms returns FALSE
T6778 8927:588.352 JLINK_HasError()
T6778 8927:588.352 JLINK_IsHalted()
T6778 8927:589.376 - 0.515ms returns FALSE
T6778 8927:688.704 JLINK_HasError()
T6778 8927:688.704 JLINK_HasError()
T6778 8927:688.704 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8927:688.704   Data:  4F 60 4B BA
T6778 8927:688.704   Debug reg: DWT_CYCCNT
T6778 8927:688.704 - 0.030ms returns 1 (0x1)
T2228 8927:691.776 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8927:691.776   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8927:691.776   Data:  00 08 00 20
T2228 8927:691.776 - 0.446ms returns 4 (0x4)
T2228 8927:692.800 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8927:692.800   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8927:692.800   Data:  00 08 00 20
T2228 8927:692.800 - 0.345ms returns 4 (0x4)
T6778 8927:696.896 JLINK_IsHalted()
T6778 8927:696.896 - 0.325ms returns FALSE
T6778 8927:797.248 JLINK_HasError()
T6778 8927:797.248 JLINK_IsHalted()
T6778 8927:798.272 - 0.397ms returns FALSE
T6778 8927:897.600 JLINK_HasError()
T6778 8927:897.600 JLINK_IsHalted()
T6778 8927:898.624 - 0.440ms returns FALSE
T6778 8927:998.976 JLINK_HasError()
T6778 8927:998.976 JLINK_IsHalted()
T6778 8928:000.000 - 0.347ms returns FALSE
T6778 8928:099.328 JLINK_HasError()
T6778 8928:099.328 JLINK_IsHalted()
T6778 8928:100.352 - 0.514ms returns FALSE
T6778 8928:200.704 JLINK_HasError()
T6778 8928:200.704 JLINK_IsHalted()
T6778 8928:201.728 - 0.410ms returns FALSE
T6778 8928:302.080 JLINK_HasError()
T6778 8928:302.080 JLINK_HasError()
T6778 8928:302.080 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8928:302.080   Data:  4F 60 4B BA
T6778 8928:302.080   Debug reg: DWT_CYCCNT
T6778 8928:302.080 - 0.043ms returns 1 (0x1)
T2228 8928:307.200 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8928:307.200   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8928:307.200   Data:  00 08 00 20
T2228 8928:307.200 - 0.386ms returns 4 (0x4)
T2228 8928:307.200 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8928:307.200   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8928:307.200   Data:  00 08 00 20
T2228 8928:307.200 - 0.362ms returns 4 (0x4)
T6778 8928:311.296 JLINK_IsHalted()
T6778 8928:311.296 - 0.495ms returns FALSE
T6778 8928:411.648 JLINK_HasError()
T6778 8928:411.648 JLINK_IsHalted()
T6778 8928:412.672 - 0.658ms returns FALSE
T6778 8928:513.024 JLINK_HasError()
T6778 8928:513.024 JLINK_IsHalted()
T6778 8928:514.048 - 0.385ms returns FALSE
T6778 8928:614.400 JLINK_HasError()
T6778 8928:614.400 JLINK_IsHalted()
T6778 8928:614.400 - 0.376ms returns FALSE
T6778 8928:714.752 JLINK_HasError()
T6778 8928:715.776 JLINK_IsHalted()
T6778 8928:715.776 - 0.340ms returns FALSE
T6778 8928:816.128 JLINK_HasError()
T6778 8928:816.128 JLINK_HasError()
T6778 8928:816.128 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8928:816.128   Data:  4F 60 4B BA
T6778 8928:816.128   Debug reg: DWT_CYCCNT
T6778 8928:816.128 - 0.050ms returns 1 (0x1)
T2228 8928:819.200 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8928:819.200   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8928:819.200   Data:  00 08 00 20
T2228 8928:819.200 - 0.379ms returns 4 (0x4)
T2228 8928:819.200 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8928:819.200   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8928:819.200   Data:  00 08 00 20
T2228 8928:819.200 - 0.295ms returns 4 (0x4)
T6778 8928:823.296 JLINK_IsHalted()
T6778 8928:823.296 - 0.296ms returns FALSE
T6778 8928:923.648 JLINK_HasError()
T6778 8928:923.648 JLINK_IsHalted()
T6778 8928:924.672 - 0.478ms returns FALSE
T6778 8929:024.000 JLINK_HasError()
T6778 8929:024.000 JLINK_IsHalted()
T6778 8929:025.024 - 0.354ms returns FALSE
T6778 8929:126.400 JLINK_HasError()
T6778 8929:126.400 JLINK_IsHalted()
T6778 8929:126.400 - 0.530ms returns FALSE
T6778 8929:227.776 JLINK_HasError()
T6778 8929:227.776 JLINK_IsHalted()
T6778 8929:227.776 - 0.386ms returns FALSE
T6778 8929:328.128 JLINK_HasError()
T6778 8929:328.128 JLINK_IsHalted()
T6778 8929:329.152 - 0.365ms returns FALSE
T6778 8929:429.504 JLINK_HasError()
T6778 8929:429.504 JLINK_HasError()
T6778 8929:429.504 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8929:429.504   Data:  4F 60 4B BA
T6778 8929:429.504   Debug reg: DWT_CYCCNT
T6778 8929:429.504 - 0.031ms returns 1 (0x1)
T2228 8929:431.552 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8929:431.552   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8929:432.576   Data:  00 08 00 20
T2228 8929:432.576 - 0.431ms returns 4 (0x4)
T2228 8929:432.576 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8929:432.576   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8929:432.576   Data:  00 08 00 20
T2228 8929:432.576 - 0.360ms returns 4 (0x4)
T6778 8929:436.672 JLINK_IsHalted()
T6778 8929:437.696 - 0.333ms returns FALSE
T6778 8929:537.024 JLINK_HasError()
T6778 8929:537.024 JLINK_IsHalted()
T6778 8929:538.048 - 0.430ms returns FALSE
T6778 8929:638.400 JLINK_HasError()
T6778 8929:638.400 JLINK_IsHalted()
T6778 8929:638.400 - 0.495ms returns FALSE
T6778 8929:740.800 JLINK_HasError()
T6778 8929:740.800 JLINK_IsHalted()
T6778 8929:740.800 - 0.416ms returns FALSE
T6778 8929:841.152 JLINK_HasError()
T6778 8929:841.152 JLINK_IsHalted()
T6778 8929:841.152 - 0.350ms returns FALSE
T6778 8929:941.504 JLINK_HasError()
T6778 8929:941.504 JLINK_HasError()
T6778 8929:941.504 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8929:941.504   Data:  4F 60 4B BA
T6778 8929:941.504   Debug reg: DWT_CYCCNT
T6778 8929:941.504 - 0.038ms returns 1 (0x1)
T2228 8929:944.576 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8929:944.576   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8929:944.576   Data:  00 08 00 20
T2228 8929:944.576 - 0.560ms returns 4 (0x4)
T2228 8929:945.600 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8929:945.600   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8929:945.600   Data:  00 08 00 20
T2228 8929:945.600 - 0.324ms returns 4 (0x4)
T6778 8929:949.696 JLINK_IsHalted()
T6778 8929:950.720 - 0.320ms returns FALSE
T6778 8930:052.096 JLINK_HasError()
T6778 8930:052.096 JLINK_IsHalted()
T6778 8930:052.096 - 0.593ms returns FALSE
T6778 8930:152.448 JLINK_HasError()
T6778 8930:152.448 JLINK_IsHalted()
T6778 8930:153.472 - 0.315ms returns FALSE
T6778 8930:252.800 JLINK_HasError()
T6778 8930:252.800 JLINK_IsHalted()
T6778 8930:253.824 - 0.455ms returns FALSE
T6778 8930:355.200 JLINK_HasError()
T6778 8930:355.200 JLINK_IsHalted()
T6778 8930:355.200 - 0.437ms returns FALSE
T6778 8930:455.552 JLINK_HasError()
T6778 8930:455.552 JLINK_HasError()
T6778 8930:455.552 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8930:455.552   Data:  4F 60 4B BA
T6778 8930:455.552   Debug reg: DWT_CYCCNT
T6778 8930:455.552 - 0.056ms returns 1 (0x1)
T2228 8930:461.696 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8930:461.696   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8930:461.696   Data:  00 08 00 20
T2228 8930:461.696 - 0.454ms returns 4 (0x4)
T2228 8930:461.696 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8930:461.696   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8930:461.696   Data:  00 08 00 20
T2228 8930:461.696 - 0.291ms returns 4 (0x4)
T6778 8930:465.792 JLINK_IsHalted()
T6778 8930:465.792 - 0.285ms returns FALSE
T6778 8930:565.120 JLINK_HasError()
T6778 8930:566.144 JLINK_IsHalted()
T6778 8930:566.144 - 0.411ms returns FALSE
T6778 8930:666.496 JLINK_HasError()
T6778 8930:666.496 JLINK_IsHalted()
T6778 8930:666.496 - 0.342ms returns FALSE
T6778 8930:766.848 JLINK_HasError()
T6778 8930:767.872 JLINK_IsHalted()
T6778 8930:767.872 - 0.566ms returns FALSE
T6778 8930:868.224 JLINK_HasError()
T6778 8930:868.224 JLINK_IsHalted()
T6778 8930:869.248 - 0.701ms returns FALSE
T6778 8930:969.600 JLINK_HasError()
T6778 8930:969.600 JLINK_IsHalted()
T6778 8930:970.624 - 0.354ms returns FALSE
T6778 8931:072.000 JLINK_HasError()
T6778 8931:072.000 JLINK_HasError()
T6778 8931:072.000 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8931:072.000   Data:  4F 60 4B BA
T6778 8931:072.000   Debug reg: DWT_CYCCNT
T6778 8931:072.000 - 0.044ms returns 1 (0x1)
T2228 8931:074.048 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8931:074.048   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8931:075.072   Data:  00 08 00 20
T2228 8931:075.072 - 0.795ms returns 4 (0x4)
T2228 8931:075.072 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8931:075.072   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8931:076.096   Data:  00 08 00 20
T2228 8931:076.096 - 0.351ms returns 4 (0x4)
T6778 8931:080.192 JLINK_IsHalted()
T6778 8931:080.192 - 0.528ms returns FALSE
T6778 8931:181.568 JLINK_HasError()
T6778 8931:181.568 JLINK_IsHalted()
T6778 8931:182.592 - 0.393ms returns FALSE
T6778 8931:281.920 JLINK_HasError()
T6778 8931:281.920 JLINK_IsHalted()
T6778 8931:282.944 - 0.494ms returns FALSE
T6778 8931:383.296 JLINK_HasError()
T6778 8931:383.296 JLINK_IsHalted()
T6778 8931:384.320 - 0.487ms returns FALSE
T6778 8931:485.696 JLINK_HasError()
T6778 8931:485.696 JLINK_IsHalted()
T6778 8931:485.696 - 0.347ms returns FALSE
T6778 8931:586.048 JLINK_HasError()
T6778 8931:586.048 JLINK_HasError()
T6778 8931:586.048 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8931:586.048   Data:  4F 60 4B BA
T6778 8931:586.048   Debug reg: DWT_CYCCNT
T6778 8931:586.048 - 0.056ms returns 1 (0x1)
T2228 8931:591.168 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8931:591.168   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8931:591.168   Data:  00 08 00 20
T2228 8931:591.168 - 0.440ms returns 4 (0x4)
T2228 8931:591.168 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8931:591.168   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8931:592.192   Data:  00 08 00 20
T2228 8931:592.192 - 0.423ms returns 4 (0x4)
T6778 8931:595.264 JLINK_IsHalted()
T6778 8931:596.288 - 0.452ms returns FALSE
T6778 8931:696.640 JLINK_HasError()
T6778 8931:696.640 JLINK_IsHalted()
T6778 8931:696.640 - 0.337ms returns FALSE
T6778 8931:796.992 JLINK_HasError()
T6778 8931:796.992 JLINK_IsHalted()
T6778 8931:798.016 - 0.549ms returns FALSE
T6778 8931:898.368 JLINK_HasError()
T6778 8931:899.392 JLINK_IsHalted()
T6778 8931:899.392 - 0.545ms returns FALSE
T6778 8931:999.744 JLINK_HasError()
T6778 8932:000.768 JLINK_IsHalted()
T6778 8932:000.768 - 0.439ms returns FALSE
T6778 8932:101.120 JLINK_HasError()
T6778 8932:101.120 JLINK_HasError()
T6778 8932:101.120 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8932:101.120   Data:  4F 60 4B BA
T6778 8932:101.120   Debug reg: DWT_CYCCNT
T6778 8932:101.120 - 0.041ms returns 1 (0x1)
T2228 8932:105.216 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8932:106.240   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8932:106.240   Data:  00 08 00 20
T2228 8932:106.240 - 0.451ms returns 4 (0x4)
T2228 8932:106.240 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8932:106.240   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8932:107.264   Data:  00 08 00 20
T2228 8932:107.264 - 0.351ms returns 4 (0x4)
T6778 8932:110.336 JLINK_IsHalted()
T6778 8932:111.360 - 0.501ms returns FALSE
T6778 8932:211.712 JLINK_HasError()
T6778 8932:211.712 JLINK_IsHalted()
T6778 8932:212.736 - 0.488ms returns FALSE
T6778 8932:313.088 JLINK_HasError()
T6778 8932:313.088 JLINK_IsHalted()
T6778 8932:313.088 - 0.452ms returns FALSE
T6778 8932:413.440 JLINK_HasError()
T6778 8932:413.440 JLINK_IsHalted()
T6778 8932:414.464 - 0.355ms returns FALSE
T6778 8932:514.816 JLINK_HasError()
T6778 8932:514.816 JLINK_IsHalted()
T6778 8932:514.816 - 0.423ms returns FALSE
T6778 8932:616.192 JLINK_HasError()
T6778 8932:616.192 JLINK_HasError()
T6778 8932:616.192 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8932:616.192   Data:  4F 60 4B BA
T6778 8932:616.192   Debug reg: DWT_CYCCNT
T6778 8932:616.192 - 0.068ms returns 1 (0x1)
T2228 8932:620.288 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8932:620.288   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8932:620.288   Data:  00 08 00 20
T2228 8932:620.288 - 0.583ms returns 4 (0x4)
T2228 8932:620.288 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8932:620.288   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8932:620.288   Data:  00 08 00 20
T2228 8932:620.288 - 0.368ms returns 4 (0x4)
T6778 8932:624.384 JLINK_IsHalted()
T6778 8932:624.384 - 0.347ms returns FALSE
T6778 8932:724.736 JLINK_HasError()
T6778 8932:724.736 JLINK_IsHalted()
T6778 8932:725.760 - 0.618ms returns FALSE
T6778 8932:826.112 JLINK_HasError()
T6778 8932:826.112 JLINK_IsHalted()
T6778 8932:827.136 - 0.616ms returns FALSE
T6778 8932:927.488 JLINK_HasError()
T6778 8932:927.488 JLINK_IsHalted()
T6778 8932:928.512 - 0.516ms returns FALSE
T6778 8933:029.888 JLINK_HasError()
T6778 8933:029.888 JLINK_IsHalted()
T6778 8933:029.888 - 0.360ms returns FALSE
T6778 8933:130.240 JLINK_HasError()
T6778 8933:130.240 JLINK_HasError()
T6778 8933:130.240 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8933:130.240   Data:  4F 60 4B BA
T6778 8933:130.240   Debug reg: DWT_CYCCNT
T6778 8933:130.240 - 0.058ms returns 1 (0x1)
T2228 8933:134.336 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8933:134.336   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8933:134.336   Data:  00 08 00 20
T2228 8933:134.336 - 0.656ms returns 4 (0x4)
T2228 8933:135.360 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8933:135.360   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8933:135.360   Data:  00 08 00 20
T2228 8933:135.360 - 0.369ms returns 4 (0x4)
T6778 8933:138.432 JLINK_IsHalted()
T6778 8933:138.432 - 0.446ms returns FALSE
T6778 8933:239.808 JLINK_HasError()
T6778 8933:239.808 JLINK_IsHalted()
T6778 8933:240.832 - 0.897ms returns FALSE
T6778 8933:341.184 JLINK_HasError()
T6778 8933:341.184 JLINK_IsHalted()
T6778 8933:342.208 - 0.528ms returns FALSE
T6778 8933:443.584 JLINK_HasError()
T6778 8933:443.584 JLINK_IsHalted()
T6778 8933:443.584 - 0.383ms returns FALSE
T6778 8933:544.960 JLINK_HasError()
T6778 8933:544.960 JLINK_IsHalted()
T6778 8933:545.984 - 0.438ms returns FALSE
T6778 8933:645.312 JLINK_HasError()
T6778 8933:645.312 JLINK_HasError()
T6778 8933:645.312 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8933:646.336   Data:  4F 60 4B BA
T6778 8933:646.336   Debug reg: DWT_CYCCNT
T6778 8933:646.336 - 0.166ms returns 1 (0x1)
T2228 8933:650.432 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8933:650.432   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8933:650.432   Data:  00 08 00 20
T2228 8933:650.432 - 0.446ms returns 4 (0x4)
T2228 8933:651.456 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8933:651.456   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8933:651.456   Data:  00 08 00 20
T2228 8933:651.456 - 0.447ms returns 4 (0x4)
T6778 8933:654.528 JLINK_IsHalted()
T6778 8933:654.528 - 0.535ms returns FALSE
T6778 8933:755.904 JLINK_HasError()
T6778 8933:755.904 JLINK_IsHalted()
T6778 8933:755.904 - 0.392ms returns FALSE
T6778 8933:857.280 JLINK_HasError()
T6778 8933:857.280 JLINK_IsHalted()
T6778 8933:857.280 - 0.492ms returns FALSE
T6778 8933:957.632 JLINK_HasError()
T6778 8933:957.632 JLINK_IsHalted()
T6778 8933:958.656 - 0.338ms returns FALSE
T6778 8934:057.984 JLINK_HasError()
T6778 8934:057.984 JLINK_IsHalted()
T6778 8934:059.008 - 0.610ms returns FALSE
T6778 8934:160.384 JLINK_HasError()
T6778 8934:160.384 JLINK_HasError()
T6778 8934:160.384 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8934:160.384   Data:  4F 60 4B BA
T6778 8934:160.384   Debug reg: DWT_CYCCNT
T6778 8934:160.384 - 0.031ms returns 1 (0x1)
T2228 8934:162.432 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8934:162.432   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8934:162.432   Data:  00 08 00 20
T2228 8934:162.432 - 0.402ms returns 4 (0x4)
T2228 8934:162.432 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8934:162.432   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8934:163.456   Data:  00 08 00 20
T2228 8934:163.456 - 0.326ms returns 4 (0x4)
T6778 8934:166.528 JLINK_IsHalted()
T6778 8934:166.528 - 0.383ms returns FALSE
T6778 8934:266.880 JLINK_HasError()
T6778 8934:266.880 JLINK_IsHalted()
T6778 8934:266.880 - 0.316ms returns FALSE
T6778 8934:368.256 JLINK_HasError()
T6778 8934:368.256 JLINK_IsHalted()
T6778 8934:369.280 - 0.431ms returns FALSE
T6778 8934:468.608 JLINK_HasError()
T6778 8934:468.608 JLINK_IsHalted()
T6778 8934:469.632 - 0.350ms returns FALSE
T6778 8934:569.984 JLINK_HasError()
T6778 8934:569.984 JLINK_IsHalted()
T6778 8934:571.008 - 0.482ms returns FALSE
T6778 8934:671.360 JLINK_HasError()
T6778 8934:671.360 JLINK_IsHalted()
T6778 8934:672.384 - 0.376ms returns FALSE
T6778 8934:771.712 JLINK_HasError()
T6778 8934:771.712 JLINK_HasError()
T6778 8934:771.712 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8934:771.712   Data:  4F 60 4B BA
T6778 8934:771.712   Debug reg: DWT_CYCCNT
T6778 8934:771.712 - 0.046ms returns 1 (0x1)
T2228 8934:774.784 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8934:774.784   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8934:774.784   Data:  00 08 00 20
T2228 8934:774.784 - 0.599ms returns 4 (0x4)
T2228 8934:774.784 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8934:774.784   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8934:775.808   Data:  00 08 00 20
T2228 8934:775.808 - 0.819ms returns 4 (0x4)
T6778 8934:781.952 JLINK_IsHalted()
T6778 8934:781.952 - 0.394ms returns FALSE
T6778 8934:882.304 JLINK_HasError()
T6778 8934:882.304 JLINK_IsHalted()
T6778 8934:883.328 - 0.632ms returns FALSE
T6778 8934:983.680 JLINK_HasError()
T6778 8934:983.680 JLINK_IsHalted()
T6778 8934:984.704 - 0.386ms returns FALSE
T6778 8935:086.080 JLINK_HasError()
T6778 8935:086.080 JLINK_IsHalted()
T6778 8935:086.080 - 0.467ms returns FALSE
T6778 8935:187.456 JLINK_HasError()
T6778 8935:187.456 JLINK_IsHalted()
T6778 8935:188.480 - 0.482ms returns FALSE
T6778 8935:287.808 JLINK_HasError()
T6778 8935:287.808 JLINK_HasError()
T6778 8935:287.808 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8935:287.808   Data:  4F 60 4B BA
T6778 8935:288.832   Debug reg: DWT_CYCCNT
T6778 8935:288.832 - 0.039ms returns 1 (0x1)
T2228 8935:290.880 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8935:290.880   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8935:291.904   Data:  00 08 00 20
T2228 8935:291.904 - 0.493ms returns 4 (0x4)
T2228 8935:291.904 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8935:291.904   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8935:291.904   Data:  00 08 00 20
T2228 8935:291.904 - 0.493ms returns 4 (0x4)
T6778 8935:296.000 JLINK_IsHalted()
T6778 8935:296.000 - 0.363ms returns FALSE
T6778 8935:396.352 JLINK_HasError()
T6778 8935:396.352 JLINK_IsHalted()
T6778 8935:397.376 - 0.391ms returns FALSE
T6778 8935:497.728 JLINK_HasError()
T6778 8935:497.728 JLINK_IsHalted()
T6778 8935:497.728 - 0.345ms returns FALSE
T6778 8935:598.080 JLINK_HasError()
T6778 8935:598.080 JLINK_IsHalted()
T6778 8935:598.080 - 0.543ms returns FALSE
T6778 8935:700.480 JLINK_HasError()
T6778 8935:700.480 JLINK_IsHalted()
T6778 8935:700.480 - 0.467ms returns FALSE
T6778 8935:800.832 JLINK_HasError()
T6778 8935:800.832 JLINK_HasError()
T6778 8935:800.832 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8935:800.832   Data:  4F 60 4B BA
T6778 8935:800.832   Debug reg: DWT_CYCCNT
T6778 8935:800.832 - 0.031ms returns 1 (0x1)
T2228 8935:804.928 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8935:804.928   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8935:805.952   Data:  00 08 00 20
T2228 8935:805.952 - 0.545ms returns 4 (0x4)
T2228 8935:805.952 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8935:805.952   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8935:806.976   Data:  00 08 00 20
T2228 8935:806.976 - 0.415ms returns 4 (0x4)
T6778 8935:810.048 JLINK_IsHalted()
T6778 8935:810.048 - 0.364ms returns FALSE
T6778 8935:911.424 JLINK_HasError()
T6778 8935:911.424 JLINK_IsHalted()
T6778 8935:911.424 - 0.563ms returns FALSE
T6778 8936:012.800 JLINK_HasError()
T6778 8936:012.800 JLINK_IsHalted()
T6778 8936:012.800 - 0.349ms returns FALSE
T6778 8936:114.176 JLINK_HasError()
T6778 8936:114.176 JLINK_IsHalted()
T6778 8936:114.176 - 0.367ms returns FALSE
T6778 8936:214.528 JLINK_HasError()
T6778 8936:214.528 JLINK_IsHalted()
T6778 8936:215.552 - 0.334ms returns FALSE
T6778 8936:316.928 JLINK_HasError()
T6778 8936:316.928 JLINK_HasError()
T6778 8936:316.928 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8936:316.928   Data:  4F 60 4B BA
T6778 8936:316.928   Debug reg: DWT_CYCCNT
T6778 8936:316.928 - 0.038ms returns 1 (0x1)
T2228 8936:320.000 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8936:320.000   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8936:320.000   Data:  00 08 00 20
T2228 8936:320.000 - 0.460ms returns 4 (0x4)
T2228 8936:320.000 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8936:320.000   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8936:321.024   Data:  00 08 00 20
T2228 8936:321.024 - 0.323ms returns 4 (0x4)
T6778 8936:324.096 JLINK_IsHalted()
T6778 8936:324.096 - 0.341ms returns FALSE
T6778 8936:425.472 JLINK_HasError()
T6778 8936:425.472 JLINK_IsHalted()
T6778 8936:425.472 - 0.358ms returns FALSE
T6778 8936:525.824 JLINK_HasError()
T6778 8936:525.824 JLINK_IsHalted()
T6778 8936:525.824 - 0.340ms returns FALSE
T6778 8936:626.176 JLINK_HasError()
T6778 8936:626.176 JLINK_IsHalted()
T6778 8936:626.176 - 0.451ms returns FALSE
T6778 8936:727.552 JLINK_HasError()
T6778 8936:727.552 JLINK_IsHalted()
T6778 8936:727.552 - 0.392ms returns FALSE
T6778 8936:827.904 JLINK_HasError()
T6778 8936:827.904 JLINK_IsHalted()
T6778 8936:828.928 - 0.426ms returns FALSE
T6778 8936:929.280 JLINK_HasError()
T6778 8936:929.280 JLINK_HasError()
T6778 8936:929.280 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8936:929.280   Data:  4F 60 4B BA
T6778 8936:929.280   Debug reg: DWT_CYCCNT
T6778 8936:929.280 - 0.028ms returns 1 (0x1)
T2228 8936:931.328 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8936:931.328   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8936:931.328   Data:  00 08 00 20
T2228 8936:931.328 - 0.419ms returns 4 (0x4)
T2228 8936:931.328 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8936:931.328   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8936:932.352   Data:  00 08 00 20
T2228 8936:932.352 - 0.341ms returns 4 (0x4)
T6778 8936:934.400 JLINK_IsHalted()
T6778 8936:935.424 - 0.310ms returns FALSE
T6778 8937:035.776 JLINK_HasError()
T6778 8937:035.776 JLINK_IsHalted()
T6778 8937:035.776 - 0.458ms returns FALSE
T6778 8937:137.152 JLINK_HasError()
T6778 8937:137.152 JLINK_IsHalted()
T6778 8937:137.152 - 0.356ms returns FALSE
T6778 8937:237.504 JLINK_HasError()
T6778 8937:237.504 JLINK_IsHalted()
T6778 8937:238.528 - 0.300ms returns FALSE
T6778 8937:338.880 JLINK_HasError()
T6778 8937:338.880 JLINK_IsHalted()
T6778 8937:338.880 - 0.388ms returns FALSE
T6778 8937:440.256 JLINK_HasError()
T6778 8937:440.256 JLINK_HasError()
T6778 8937:440.256 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8937:440.256   Data:  4F 60 4B BA
T6778 8937:440.256   Debug reg: DWT_CYCCNT
T6778 8937:440.256 - 0.028ms returns 1 (0x1)
T2228 8937:461.760 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8937:461.760   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8937:462.784   Data:  00 08 00 20
T2228 8937:462.784 - 0.440ms returns 4 (0x4)
T2228 8937:462.784 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8937:462.784   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8937:462.784   Data:  00 08 00 20
T2228 8937:462.784 - 0.435ms returns 4 (0x4)
T6778 8937:466.880 JLINK_IsHalted()
T6778 8937:466.880 - 0.347ms returns FALSE
T6778 8937:568.256 JLINK_HasError()
T6778 8937:568.256 JLINK_IsHalted()
T6778 8937:568.256 - 0.458ms returns FALSE
T6778 8937:668.608 JLINK_HasError()
T6778 8937:668.608 JLINK_IsHalted()
T6778 8937:668.608 - 0.378ms returns FALSE
T6778 8937:768.960 JLINK_HasError()
T6778 8937:768.960 JLINK_IsHalted()
T6778 8937:768.960 - 0.371ms returns FALSE
T6778 8937:870.336 JLINK_HasError()
T6778 8937:870.336 JLINK_IsHalted()
T6778 8937:870.336 - 0.430ms returns FALSE
T6778 8937:971.712 JLINK_HasError()
T6778 8937:971.712 JLINK_HasError()
T6778 8937:971.712 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8937:971.712   Data:  4F 60 4B BA
T6778 8937:971.712   Debug reg: DWT_CYCCNT
T6778 8937:971.712 - 0.029ms returns 1 (0x1)
T2228 8937:973.760 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8937:973.760   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8937:973.760   Data:  00 08 00 20
T2228 8937:973.760 - 0.636ms returns 4 (0x4)
T2228 8937:973.760 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8937:973.760   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8937:973.760   Data:  00 08 00 20
T2228 8937:973.760 - 0.401ms returns 4 (0x4)
T6778 8937:977.856 JLINK_IsHalted()
T6778 8937:977.856 - 0.470ms returns FALSE
T6778 8938:079.232 JLINK_HasError()
T6778 8938:079.232 JLINK_IsHalted()
T6778 8938:079.232 - 0.482ms returns FALSE
T2228 8938:084.352 JLINK_HasError()
T2228 8938:084.352 JLINK_ClrBPEx(BPHandle = 0x00000005)
T2228 8938:084.352   CPU is running
T2228 8938:084.352   CPU is running
T2228 8938:084.352   CPU_WriteMem(4 bytes @ 0xE0002008)
T2228 8938:085.376   CPU is running
T2228 8938:085.376   CPU_WriteMem(4 bytes @ 0xE000200C)
T2228 8938:085.376   CPU is running
T2228 8938:085.376   CPU_WriteMem(4 bytes @ 0xE0002010)
T2228 8938:086.400   CPU is running
T2228 8938:086.400   CPU_WriteMem(4 bytes @ 0xE0002014)
T2228 8938:087.424   CPU is running
T2228 8938:087.424   CPU_WriteMem(4 bytes @ 0xE0002018)
T2228 8938:087.424   CPU is running
T2228 8938:087.424   CPU_WriteMem(4 bytes @ 0xE000201C)
T2228 8938:087.424 - 3.145ms returns 0x00
T6778 8938:179.584 JLINK_HasError()
T6778 8938:179.584 JLINK_IsHalted()
T6778 8938:179.584 - 0.489ms returns FALSE
T6778 8938:280.960 JLINK_HasError()
T6778 8938:280.960 JLINK_IsHalted()
T6778 8938:280.960 - 0.345ms returns FALSE
T6778 8938:381.312 JLINK_HasError()
T6778 8938:381.312 JLINK_IsHalted()
T6778 8938:381.312 - 0.340ms returns FALSE
T6778 8938:481.664 JLINK_HasError()
T6778 8938:481.664 JLINK_IsHalted()
T6778 8938:482.688 - 0.315ms returns FALSE
T6778 8938:583.040 JLINK_HasError()
T6778 8938:583.040 JLINK_HasError()
T6778 8938:583.040 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T6778 8938:583.040   Data:  4F 60 4B BA
T6778 8938:583.040   Debug reg: DWT_CYCCNT
T6778 8938:583.040 - 0.045ms returns 1 (0x1)
T2228 8938:586.112 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8938:586.112   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8938:587.136   Data:  00 08 00 20
T2228 8938:587.136 - 0.526ms returns 4 (0x4)
T2228 8938:587.136 JLINK_ReadMemEx(0x20003100, 0x4 Bytes, Flags = 0x02000000)
T2228 8938:587.136   CPU_ReadMem(4 bytes @ 0x20003100)
T2228 8938:588.160   Data:  00 08 00 20
T2228 8938:588.160 - 0.487ms returns 4 (0x4)
T6778 8938:591.232 JLINK_IsHalted()
T6778 8938:591.232 - 0.348ms returns FALSE
T6778 8938:691.584 JLINK_HasError()
T6778 8938:691.584 JLINK_IsHalted()
T6778 8938:692.608 - 0.556ms returns FALSE
T6778 8938:792.960 JLINK_HasError()
T6778 8938:792.960 JLINK_Halt()
T6778 8938:796.032 - 2.739ms returns 0x00
T6778 8938:796.032 JLINK_IsHalted()
T6778 8938:796.032 - 0.005ms returns TRUE
T6778 8938:796.032 JLINK_IsHalted()
T6778 8938:796.032 - 0.004ms returns TRUE
T6778 8938:796.032 JLINK_IsHalted()
T6778 8938:796.032 - 0.004ms returns TRUE
T6778 8938:796.032 JLINK_HasError()
T6778 8938:796.032 JLINK_ReadReg(R15 (PC))
T6778 8938:796.032 - 0.007ms returns 0x0000C44E
T6778 8938:796.032 JLINK_ReadReg(XPSR)
T6778 8938:796.032 - 0.004ms returns 0x61000000
T6778 8938:796.032 JLINK_HasError()
T6778 8938:796.032 JLINK_HasError()
T6778 8938:796.032 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T6778 8938:796.032   CPU_ReadMem(4 bytes @ 0xE000ED30)
T6778 8938:796.032   Data:  01 00 00 00
T6778 8938:796.032 - 0.392ms returns 1 (0x1)
T6778 8938:796.032 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T6778 8938:796.032   CPU_ReadMem(4 bytes @ 0xE0001028)
T6778 8938:796.032   Data:  00 00 00 00
T6778 8938:796.032   Debug reg: DWT_FUNC[0]
T6778 8938:796.032 - 0.274ms returns 1 (0x1)
T6778 8938:796.032 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T6778 8938:796.032   CPU_ReadMem(4 bytes @ 0xE0001038)
T6778 8938:797.056   Data:  00 02 00 00
T6778 8938:797.056   Debug reg: DWT_FUNC[1]
T6778 8938:797.056 - 0.286ms returns 1 (0x1)
T6778 8938:797.056 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T6778 8938:797.056   CPU_ReadMem(4 bytes @ 0xE0001048)
T6778 8938:797.056   Data:  00 00 00 00
T6778 8938:797.056   Debug reg: DWT_FUNC[2]
T6778 8938:797.056 - 0.305ms returns 1 (0x1)
T6778 8938:797.056 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T6778 8938:797.056   CPU_ReadMem(4 bytes @ 0xE0001058)
T6778 8938:797.056   Data:  00 00 00 00
T6778 8938:797.056   Debug reg: DWT_FUNC[3]
T6778 8938:797.056 - 0.317ms returns 1 (0x1)
T2228 8939:445.248 JLINK_HasError()
T2228 8939:456.512 JLINK_Close()
T2228 8939:456.512   CPU_ReadMem(4 bytes @ 0x********)
T2228 8939:468.800 - 12.715ms
T2228 8939:468.800   
T2228 8939:468.800   Closed
