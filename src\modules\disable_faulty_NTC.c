#include "type.h"
#include "board_config.h"
#include "board.h"
#include "hal_os.h"
#include "battery_dev.h"
#include <stddef.h>
#include <string.h>



/*
温感滤波策略设计要求：
处理方案：软件新增误报判断。
具体方案：
1、当出现温感采集数值出现往下跳变，且跳变
速率≥8℃/次，则该温感采集数值暂时屏蔽不参与程序中关于温
度相关的策略（温度显示使用平均温度值代替）;
2、单个 BMS 主机采用上文描述策略：
①当采集温度点数量为 1 个时，不采取上述策略；
②当采集温度点数量为 2 个时，最多可暂时屏蔽温感
数量 1 个;
③当采集温度点数量≥3 个时，最多可暂时屏蔽温感
数量≤2 个;
3、恢复使用条件，被暂时屏蔽的温感采集到的温度数
值＞(其他启用的温感平均值-5℃)&持续时间≥10S，则该温感实
际采集值可以再次被启用;
4、被暂时屏蔽的温感，下电时需要保存记录，下次上电
时，需要保持被屏蔽状态（显示温度使用平均温度值代替），上
电后按照上文内容判断是否恢复启用;
*/
/*============= 宏定义 =============*/
#define MAX_NTC_NUM           12     //最大NTC个数
#define JUMP_THRESHOLD        80     // 8℃/次跳变阈值
#define RECOVERY_TEMP_OFFSET  (-50)  // 恢复温度偏移
#define RECOVERY_TIME_MS      10000  // 10秒恢复时间
#define FLAG_MASK_QUANTITY    0xFC00 // 高6位掩码
#define FLAG_MASK_NTC_BITS    0x03FF // 低10位掩码
 
#define INVALID_TEMP    0x7FFF      // 无效温度标识（根据实际系统定义）
#define TEMP_MIN        (-400)      // 温度合理下限 0.1%
#define TEMP_MAX        1500        // 温度合理上限 0.1%
 
static SettingPara_t *para = (SettingPara_t *)SETTING_PARA_SAVE_RAM_ADDR;
 
/*============= 结构体定义 =============*/
typedef struct {
    /*---- 核心状态 ----*/
    unsigned short shielded_flags;  // 屏蔽标志位（bit0~9对应NTC1~10）
    unsigned char  shielded_count;  // 当前屏蔽的传感器数量
    
    /*---- 温度数据 ----*/
    short          prev_temps[MAX_NTC_NUM];  // 前次采样温度值（无论是否屏蔽）
    short          curr_temps[MAX_NTC_NUM];  // 当前采样温度值
    
    /*---- 时间管理 ----*/
    unsigned int   shield_start_time[MAX_NTC_NUM]; // 屏蔽开始时间戳（ms）
    
    /*---- 系统标志 ----*/
    unsigned char  is_first_sample : 1;    // 首次采样标志
    unsigned char  need_flash_save : 1;    // FLASH存储请求标志
} NTC_Shield_State;

/*============= 模块接口函数 =============*/
// 初始化模块
int ntc_shield_init(NTC_Shield_State* state);
// 主处理函数
void ntc_shield_process(NTC_Shield_State* state);
// 获取有效温度平均值
short get_valid_avg_temp(const short* temps,unsigned short flags,short ntc_amount);


/*============= 初始化函数实现 =============*/
/**
  * @brief  初始化温感屏蔽模块
  * @param  state 状态结构体指针
  * @retval 无
  */
int ntc_shield_init(NTC_Shield_State* state) 
{
    /* 参数合法性检查 */
    if (state == NULL) 
	{
        return -1; // 可添加错误日志输出
    }

    /* 清零整个状态结构体 */
    memset(state, 0, sizeof(NTC_Shield_State));

    /* 步骤1：读取持久化屏蔽标志 */
    unsigned short flash_shield_flags = 0;
    (void)get_signals_value(SID_NTC_SHIELD_FLAG, &flash_shield_flags, 1);
	
	//state->shielded_flags = flash_shield_flags & FLAG_MASK_NTC_BITS; // 确保高6位=0

    
    /* 解析有效屏蔽位（仅低10位） */
    state->shielded_flags = flash_shield_flags & FLAG_MASK_NTC_BITS;

    /* 步骤2：获取当前温感配置数量 */
    short ntc_amount = 0;
    (void)get_signals_value(SID_TEMPERATURE_AMOUNT, &ntc_amount, 1);
    
    /* 温感数量有效性校验 */
    if (ntc_amount < 1 || ntc_amount > MAX_NTC_NUM) 
	{
        state->shielded_flags = 0; // 无效数量时清空屏蔽标志
        return -2;
    }

    /* 步骤3：修剪屏蔽标志（屏蔽不存在的温感） */
    state->shielded_flags &= (1 << ntc_amount) - 1;

    /* 步骤4：验证屏蔽数量合法性 */
    unsigned char shielded_count = 0;
    for (int i = 0; i < ntc_amount; ++i) 
	{
        if (state->shielded_flags & (1 << i)) 
		{
            ++shielded_count;
        }
    }
    
    // 根据配置数量修正屏蔽状态
    if ((ntc_amount == 1 && shielded_count > 0) || 
        (ntc_amount == 2 && shielded_count > 1) ||
        (ntc_amount >= 3 && shielded_count > 2)) 
    {
        state->shielded_flags = 0; // 非法状态重置
        shielded_count = 0;
    }

    /* 步骤5：初始化屏蔽开始时间 */
    unsigned int current_time = get_sys_tick();
    for (int i = 0; i < ntc_amount; ++i) 
	{
        if (state->shielded_flags & (1 << i)) 
		{
            state->shield_start_time[i] = current_time;
        }
    }

    /* 步骤6：设置首次采样标志 */
    state->is_first_sample = 1;
	return 0;
}





/*============= 状态机实现 =============*/
typedef enum {
    STATE_INIT,
    STATE_SCANNING,
    STATE_SHIELDED
} NTC_State;
/*============= 状态处理函数 =============*/

/**
  * @brief  处理初始化状态
  * @param  state 状态结构体指针
  * @param  ntc_temp 当前温度数组
  * @param  ntc_amount 温感数量
  * @retval 下一个状态
  */
static NTC_State handle_init_state(NTC_Shield_State* state, const short* ntc_temp, short ntc_amount) 
{
    unsigned short saved_flags = 0;
    // 读取保存的屏蔽状态
    (void)get_signals_value(SID_NTC_SHIELD_FLAG, &saved_flags, 1);
    
    if((saved_flags & FLAG_MASK_QUANTITY) >> 10) 
    {
        state->shielded_flags = saved_flags;
        return STATE_SHIELDED;
    }
    
    // 保存初始温度值
    memcpy(state->prev_temps, ntc_temp, sizeof(short)*ntc_amount);
    return STATE_SCANNING;
}

/**
  * @brief  处理扫描状态
  * @param  state 状态结构体指针
  * @param  ntc_temp 当前温度数组
  * @param  ntc_amount 温感数量
  * @retval 下一个状态
  */
static NTC_State handle_scanning_state(NTC_Shield_State* state, const short* ntc_temp, short ntc_amount) 
{
    // 温度跳变检测逻辑
    for(int i = 0; i < ntc_amount; i++) 
    {
        if((state->shielded_flags & (1<<i)) == 0) 
        { // 仅检查未屏蔽的传感器
            short delta = state->prev_temps[i] - ntc_temp[i];
            if(delta >= JUMP_THRESHOLD) 
            {
                // 设置临时屏蔽标志
                unsigned short new_count = ((state->shielded_flags & FLAG_MASK_QUANTITY) >> 10) + 1;
                // 检查屏蔽数量限制
                if((ntc_amount == 2 && new_count <= 1) || (ntc_amount >= 3 && new_count <= 2)) 
                {
                    state->shielded_flags = (new_count << 10) | (state->shielded_flags & FLAG_MASK_NTC_BITS) | (1<<i);
                    state->shield_start_time[i] = get_sys_tick();
                }
            }
        }
        state->prev_temps[i] = ntc_temp[i];//存储当前值，留做下一次比较
    }
    
    return (state->shielded_flags & FLAG_MASK_QUANTITY) ? STATE_SHIELDED : STATE_SCANNING;
}

/**
  * @brief  处理屏蔽状态
  * @param  state 状态结构体指针
  * @param  ntc_temp 当前温度数组
  * @param  ntc_amount 温感数量
  * @retval 下一个状态
  */
static NTC_State handle_shielded_state(NTC_Shield_State* state, const short* ntc_temp, short ntc_amount) 
{
    // 恢复条件检查
    short valid_avg = get_valid_avg_temp(ntc_temp, state->shielded_flags, ntc_amount);
    if (valid_avg == INVALID_TEMP) 
	{
        return STATE_SHIELDED;
    }

    // 1. 处理已屏蔽传感器的恢复
    for (int i = 0; i < ntc_amount; i++) 
	{
        if (state->shielded_flags & (1 << i)) 
		{
            if (ntc_temp[i] > (valid_avg + RECOVERY_TEMP_OFFSET)) 
			{
                if ((get_sys_tick() - state->shield_start_time[i]) >= RECOVERY_TIME_MS) 
				{
                    state->shielded_flags &= ~(1 << i);
                    unsigned short new_count = ((state->shielded_flags & FLAG_MASK_QUANTITY) >> 10) - 1;
                    state->shielded_flags = (new_count << 10) | (state->shielded_flags & FLAG_MASK_NTC_BITS);
                }
            } 
			else 
			{
                state->shield_start_time[i] = get_sys_tick();
            }
        }
    }

    // 2. 新增：检测未屏蔽传感器的新异常
    for (int i = 0; i < ntc_amount; i++) 
	{
        if ((state->shielded_flags & (1 << i)) == 0) 
		{
            short delta = state->prev_temps[i] - ntc_temp[i];
            if (delta >= JUMP_THRESHOLD)
			{
                unsigned short new_count = ((state->shielded_flags & FLAG_MASK_QUANTITY) >> 10) + 1;
                if ((ntc_amount == 2 && new_count <= 1) || (ntc_amount >= 3 && new_count <= 2)) 
				{
                    state->shielded_flags = (new_count << 10) | (state->shielded_flags & FLAG_MASK_NTC_BITS) | (1 << i);
                    state->shield_start_time[i] = get_sys_tick();
                }
            }
        }
        state->prev_temps[i] = ntc_temp[i]; // 更新历史值
    }

    // 3. 更新显示和保存状态

	for (int i = 0; i < ntc_amount; i++) 
	{
		short display_temp = (state->shielded_flags & (1 << i)) ? valid_avg : ntc_temp[i];
		(void)set_signals_value(SID_CELL1_TEMPERATURE + i, &display_temp, 1);
	}
    
    (void)set_signals_value(SID_NTC_SHIELD_FLAG, &state->shielded_flags, 1);

    // 4. 动态状态转移
    return (state->shielded_flags & FLAG_MASK_NTC_BITS) ? STATE_SHIELDED : STATE_SCANNING;
}

/*============= 状态机函数 =============*/
void ntc_shield_process(NTC_Shield_State* state) 
{
    static NTC_State current_state = STATE_INIT;
    short ntc_amount = 12;
    short ntc_temp[MAX_NTC_NUM];
    
    // 获取系统数据
	unsigned short slave_amounts = 0;
	//(void)get_signals_value(SID_TEMPERATURE_AMOUNT, &ntc_amount, 1);
	for(char i=0;i<ntc_amount;i++)
	{
		ntc_temp[i] = para->sid_cell_temperature[i];
	}
	
    switch(current_state) 
    {
        case STATE_INIT:
            current_state = handle_init_state(state, ntc_temp, ntc_amount);
			(void)set_signals_value(SID_CELL1_TEMPERATURE, &ntc_temp, ntc_amount);
            break;
            
        case STATE_SCANNING:
            current_state = handle_scanning_state(state, ntc_temp, ntc_amount);
			(void)set_signals_value(SID_CELL1_TEMPERATURE, &ntc_temp, ntc_amount);
            break;
            
        case STATE_SHIELDED:
            current_state = handle_shielded_state(state, ntc_temp, ntc_amount);
            break;
            
        default:
            current_state = STATE_INIT;
            break;
    }
}
 


/*=============get_valid_avg_temp函数实现 =============*/
/**
  * @brief  计算未屏蔽温感的有效平均值
  * @param  temps      温度数组指针（需确保长度 ≥ ntc_amount）
  * @param  flags      屏蔽标志位（bit0~bit9对应NTC1~NTC10，1=屏蔽）
  * @param  ntc_amount 实际存在的温感数量（1~10）
  * @retval 有效温度平均值（全屏蔽/无效时返回INVALID_TEMP）
  */
short get_valid_avg_temp(const short* temps,unsigned short flags,short ntc_amount) 
{
    /* 参数合法性检查（三重保护） */
    if (temps == NULL || ntc_amount < 1 || ntc_amount > 10) 
	{
        return INVALID_TEMP;
    }

    int sum = 0;
    unsigned char valid_count = 0;

    /* 遍历所有已配置的温感,温度有效性检查 */
    for (short i = 0; i < ntc_amount; ++i) 
	{
        /* 检查屏蔽状态和温度有效性 */
        if (!(flags & (1 << i)) &&           // 检查屏蔽标志位
            (temps[i] >= TEMP_MIN) &&        // 温度下限校验
            (temps[i] <= TEMP_MAX))          // 温度上限校验
        {
            sum += temps[i];
            ++valid_count;
        }
    }

    /* 处理无有效数据的情况 */
    if (valid_count == 0) 
	{
        return INVALID_TEMP;
    }

    /* 计算四舍五入平均值（避免浮点运算） */
    return (short)((sum + (valid_count / 2)) / valid_count);
}
NTC_Shield_State State_FSM;
#define DIS_NTC_TICK_SET 100
//#define DEBUG_FLAG
static void disable_Faulty_NTC_task(void)
{
	static unsigned int t_ticks_save;
    unsigned int ticks = get_sys_tick();
    unsigned int ticks_delta = ticks - t_ticks_save;
    if(ticks_delta < DIS_NTC_TICK_SET)
    {
        return;
    }
	t_ticks_save = ticks;

	static char init_flag =0;
    unsigned char dbg_cfg[2] = {0};
	unsigned short combined_T_ntc_amount =0;
	static unsigned short last_combined_T_ntc_amount =0;
	
    get_signals_value(SID_DBG_CFG, &dbg_cfg, 1);
	get_signals_value(SID_T_DBG_CFG, &dbg_cfg[1], 1);
	get_signals_value(SID_PACK_TEMPER_AMOUNT, &combined_T_ntc_amount, 1);
	if(dbg_cfg[0]==1||dbg_cfg[1]==1||(last_combined_T_ntc_amount != combined_T_ntc_amount))
	{
		last_combined_T_ntc_amount = combined_T_ntc_amount;
		init_flag=0;//重新初始化
		return;	
	}
	
	unsigned short CFG = 0;
	(void)get_signals_value(SID_DISABLE_FAULTY_NTC_CFG, &CFG, 1);
	if(CFG==0)
	{
		short ntc_amount = 0;
		short ntc_temp[MAX_NTC_NUM];
		unsigned short slave_amounts = 0;
		(void)get_signals_value(SID_TEMPERATURE_AMOUNT, &ntc_amount, 1);
		// 获取系统数据
		for(char i=0;i<ntc_amount;i++)
		{
			ntc_temp[i] = para->sid_cell_temperature[i];
		}
		(void)set_signals_value(SID_CELL1_TEMPERATURE, &ntc_temp, ntc_amount);
		init_flag=0;//重新初始化
		return;
	}

	if(init_flag==0)
	{
		if(ntc_shield_init(&State_FSM)==0)
		{
			init_flag=1;
		}
		return;
	}

	ntc_shield_process(&State_FSM);

	unsigned short current_count = (State_FSM.shielded_flags & FLAG_MASK_QUANTITY) >> 10;
	(void)set_signals_value(SID_NTC_SHIELD_AMOUNT, &current_count, 1);	

}
APP_TASK_DEFINE(disable_Faulty_NTC_task,"9");