#include "n32g45x.h"
#include "board_config.h"
#include "board.h"
#include "hal_os.h"
#include "type.h"
#include <stdio.h>

//================================================ NVIC_SystemReset
void mcu_do_reset(void)
{
    NVIC_SystemReset();
}

//================================================ irq_lock
unsigned int irq_lock(void)
{
    unsigned int key;
    key = __get_PRIMASK();
    __disable_irq();
    return key;
}
void irq_unlock(unsigned int key)
{
    __set_PRIMASK(key);
}

//=================================================  Tick
unsigned int temp_sistick = 0;

void systick_config(void)
{
	int loader = 0;
	loader = SYSCLK_FREQ;
    /* setup systick timer for 1000Hz interrupts */
    if (SysTick_Config(loader / 1000U))
	{
		while (1)
		{
		}
	}
}

 APP_INIT_DEFINE(systick_config,"1");


#if defined(SYS_TICK_WDT_ENABLE)&&defined (PROGRAM_TYPE_APP)
#if !defined(SYS_TICK_WDT_TIME)
#define SYS_TICK_WDT_TIME       3000
#endif
static unsigned int s_sys_tick_wdt_count = 0;
static void sys_tick_wdt_feed(void)
{
    s_sys_tick_wdt_count = 0;
}
APP_TASK_DEFINE(sys_tick_wdt_feed,"99");
#endif
 
#if defined(SYS_TICK_WDT_ENABLE)&&defined (PROGRAM_TYPE_APP)
static unsigned int s_sys_tick_wdt_count;
#endif


void SysTick_Handler(void)
{
	temp_sistick++;
	#if defined(SYS_TICK_WDT_ENABLE)&&defined (PROGRAM_TYPE_APP)
    if (s_sys_tick_wdt_count++ > SYS_TICK_WDT_TIME)
    {
        reset_board(SWDT_TIMEOUT_RESET);
    }
    #endif
}





unsigned int HAL_GetTick(void)
{
	return temp_sistick;
}
unsigned int get_sys_tick(void)
{
    unsigned int key = irq_lock();
    unsigned int ticks = HAL_GetTick();
    irq_unlock(key);
    return ticks;
}
//=================================================  Tick_delay



#define HAL_MAX_DELAY      0xFFFFFFFFU
__weak void HAL_Delay(uint32_t Delay)
{
  uint32_t tickstart = HAL_GetTick();
  uint32_t wait = Delay;

  /* Add a freq to guarantee minimum wait */
  if (wait < HAL_MAX_DELAY)
  {
    wait += (uint32_t)(1);
  }

  while ((HAL_GetTick() - tickstart) < wait)
  {
  }
}

void sys_time_delay(unsigned int ticks)
{
    HAL_Delay(ticks);
}

void delay_us(unsigned int time_us)
{
    unsigned int last = SysTick->VAL;
    unsigned int delta = time_us*SysTick->LOAD/(1000);
	/* Wait until the given number of time have passed */
	while ((last - SysTick->VAL) < delta) {
        ;
	}
}

//=================================================  WD



//=================================================  it
void NMI_Handler(void)
{
//    printf("NMI INT accur!\r\n");
    NVIC_SystemReset();
}
void HardFault_Handler(void)
{
//    printf("HardFault INT accur!\r\n");
    NVIC_SystemReset();
}
void BusFault_Handler(void)
{
//    printf("BusFault INT accur!\r\n");
    NVIC_SystemReset();
}
void UsageFault_Handler(void)
{
//    printf("UsageFault INT accur!\r\n");
    NVIC_SystemReset();
}

void Default_Handler(void)
{
//    printf("Unexpected INT accur!\r\n");
    NVIC_SystemReset();
}



#if defined(PROGRAM_TYPE_APP)

//can1
 
#endif


