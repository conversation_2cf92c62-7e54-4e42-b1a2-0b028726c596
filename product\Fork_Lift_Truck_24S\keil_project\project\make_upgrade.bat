set VERSION=%1
set PRODUCT_NAME=BMS_
set SOFTWARE_MAGIC_NUMB=0x2e4f544b49452e2e

C:\Keil_v5\ARM\ARMCC\bin\fromelf.exe --bin -o  firmware\app.bin   Objects_app\OUTPUT\app.axf

copy  Objects_app\OUTPUT\app.hex   firmware\
copy  Objects_boot\OUTPUT\boot.hex   firmware\

..\..\..\..\open_src\lzma1900\bin\lzma.exe e firmware\app.bin firmware\lzma_app.bin -a1 -d13

..\..\..\..\tools\make_load\make_load.exe  ..\..\release\%PRODUCT_NAME%%VERSION%.bin          firmware\lzma_app.bin     %SOFTWARE_MAGIC_NUMB% "%PRODUCT_NAME%%VERSION%" 0x22 0x11 0x11
..\..\..\..\tools\hex_all\hex_all.exe                ..\..\release\%PRODUCT_NAME%%VERSION%.hex        firmware\boot.hex                  firmware\app.hex  

..\..\..\..\tools\buildBatterySrc.vbs Fork_Lift_Truck_24S Fork_Lift_Truck_24S_

::pause


