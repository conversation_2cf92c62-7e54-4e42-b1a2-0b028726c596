// .c

#include <stdint.h>
#include <stdio.h>
#include "SCM63X_CAN.h"
#include "can_SCM63X.h"

#include "drv_ring_queue.h"
#include "hal_os.h"
#include "battery_dev.h"

#include "drv_can.h"
#include "GB_T27930_20xx.c"
#include "Charger.c"
#include "LeadAcidToLiFe4.h"
#include "ADPower.h"
#include "YiJiaTongV2.0.h"
#include "longKing.h"
#include "CAN2.0C(B).h"
#include "LigaoV4.03.h"
#include "HaoSheng.h"
#include "Banyitong_V1.2.h"
#include "ShiNeng.h"
#include "TieCheng_Soc.h"
#include "XuGong_20190821.h"

/*
CommProtocol.chg			    后台充电机通讯协议选项
// GB	                      囯标充电机通讯协议
// UDAN					  铁城BMS与充电机250Kbps区分BMS停止充电原因_V2.3
// YiJiaTongV2.0             安徽益佳通充电协议(增加加热模式)20190213
// QianGaiLi                 铅改锂叉车充电通讯协议
// BanYiTong_Chg_V1.2        合肥搬易通BMS-CAN2.0A通讯协议V1.2_充电
// ADY_V1.1                  ADYPower充电机CAN通讯协议V1.1
// ShiNeng                   施能BMS通讯协议 V3.2版本（龙工）
// CAN2.0C                   充电CAN2.0C协议
// TieCheng_Soc              充电协议（增加SOC）
// LongGong_Chg              发优旦技术协议（龙工充电协议）
// XuGong_20190821           1_1_徐工叉车用锂电池需求资料（充电协议）
// HaoSheng                  豪盛BMS通信协议
// YiJiaTong_QianTuo         安徽益佳通充电协议(增加加热模式)20190213+BMS-CAN2.0A 通信协议V1.02_20190428
// YiJiaTong_QianTuo_ZOWELL  安徽益佳通充电协议(增加加热模式)20190213+BMS-CAN2.0A 通信协议V1.02_20190428+ZOWELL电协议V2.0
//ZOWELL:整车， YiJiaTong：充电  QianTuo仪表
*/

// 定义协议枚举，并指定底层类型为 uint8_t
typedef enum
{
    procesID_GB = 0,
    procesID_UDAN,
    procesID_YiJiaTongV2_0,
    procesID_QianGaiLi,
    procesID_BanYiTong_Chg_V1_2,
    procesID_ADY_V1_1,
    procesID_ShiNeng,
    procesID_CAN2_0C,
    procesID_TieCheng_Soc,
    procesID_LongGong_Chg,
    procesID_XuGong,
    procesID_HaoSheng,
    procesID_YiJiaTong_QianTuo,
    // procesID_YiJiaTong_QianTuo_ZOWELL,
    procesID_LigaoV4_03,
    procesID_MAX_ID

} procesID_t;

// 禁能#快充#整车#快充+整车#调试#屏幕
typedef enum
{
    CAN_DISABLE = 0,     // 禁止
    CAN_FAST_CHARGE = 1, // 快充
    CAN_CHARGE_ALL = 2,  // 快充+整车
    CAN_DEBUG = 3,       // 调试
    CAN_SCREEN = 4,      // 屏幕
} CAN_FUNCTION_t;

typedef struct
{
    uint8_t processID; // 协议类型
    // 波特率
    uint8_t baud; // 波特率

    void (*processInit)(void); // 协议初始化, void (*processInit)(CAN_ID ID, CANBAUD_t baudrate);
    void (*processTask)(void);
    void (*processSend)(CAN_FrameInfoTypeDef msg); // 报文处理
    //  void (*timeout_handler)(void);   //can接收超时处理
    //  int (*poll)(void);  //轮询处理   可以在这里做数据包超时检测     返回oxff表示充电结束    0继续充电      (返回1表示辅助电源要切换为24V)
} Charge_handlerType;

bool Charger_test_enable = 0;
uint8_t Charger_protocol_test_sid = procesID_UDAN; // 协议类型
uint8_t Charger_protocol_test_baud = CANBaud250k;  // 波特率
uint8_t Charger_protocol_test_can_id = can_1;      //

extern CAN_FrameQueueType CAN0_Recv_Queue;
extern CAN_FrameQueueType CAN1_Recv_Queue;
extern CAN_FrameQueueType CAN3_Recv_Queue;

CAN_FrameQueueType CAN_DcCh_Recv_Queue;
CAN_FrameQueueType CAN_Dcch_Send_Queue;
typedef void (*send_func_t)(const CAN_FrameInfoTypeDef *msg);
typedef void (*recv_func_t)(const CAN_FrameInfoTypeDef *msg);

extern int set_signals_value(unsigned short start_id, void *value, int count);
extern int get_signals_value(unsigned short start_id, void *value, int count);
extern unsigned int get_sys_tick(void);
extern int CAN1_Hard_Init(void);
void task_AssistPowermonitor_OBC(void);
// 示例函数实现
void initialize_GB(void) {AssistPowermonitorIoInit_DCC();};
void Task_process_GB(void) { Task_DCCH_CANsent_GB(); };

void initialize_LongGong_Chg(void) {}
void Task_process_LongGong_Chg(void) { Task_DCCH_longking(); };

void initialize_LeadAcidToLiFe4(void) {};
void Task_process_LeadAcidToLiFe4(void) { Task_BMS_LeadAcidToLiFe4(); };

void initialize_ADPower(void) {};
void Task_process_ADPower(void) { Task_DCCH_ADPower(); };

void initialize_EIKTO(void) {};
void Task_process_EIKTO(void) { Task_DCCH_YijiaTongV2_0(); };

void initialize_CAN2_0C_B(void) {};
void Task_process_CAN2_0C_B(void) { Task_DCCH_CAN2_0C_B(); };

void initialize_LigaoV4_03(void) {};
void Task_process_LigaoV4_03(void) { Task_DCCH_ProLigaoV4_03(); };

// void initialize_YOU_DAN(void) { /* ... */ };
// void process_YOU_DAN(void) { /*Task_DCCH_CANsent_YOU_DAN();*/ };
void initialize_HAOSHENG(void) { /* ... */ };
void Task_process_HAOSHENG(void) { Task_DCCH_HaoSheng(); };

void initialize_Banyitong(void) { /* ... */ };
void Task_process_Banyitong(void) { Task_SendMessage_Banyitong(); };

void initialize_ShiNeng(void) { Task_proInit_ShiNeng(); }; // 初始化
void Task_process_ShiNeng(void) { Task_SendMessage_ShiNeng(); }

void initialize_TieCheng(void) { Task_Init_TieCheng(); };
void Task_process_TieCheng(void) { Task_SendMessage_TieCheng(); }

void initialize_XuGong(void) { /*Task_Init_XuGong(); */ };
void Task_process_XuGong(void) { Task_SendMessage_XuGong(); }

// 协议列表
Charge_handlerType protocol_list[] =
    {
        {procesID_GB, CANBaud250k, initialize_GB, Task_process_GB, NULL},                                  // GB	                      囯标充电机通讯协议
        {procesID_UDAN, CANBaud250k, initialize_TieCheng, Task_process_TieCheng},                          // UDAN					  铁城BMS与充电机250Kbps区分BMS停止充电原因_V2.3
        {procesID_YiJiaTongV2_0, CANBaud125k, initialize_EIKTO, Task_process_EIKTO, NULL},                 // YiJiaTongV2.0             安徽益佳通充电协议(增加加热模式)20190213
        {procesID_QianGaiLi, CANBaud125k, initialize_LeadAcidToLiFe4, Task_process_LeadAcidToLiFe4, NULL}, // QianGaiLi                 铅改锂叉车充电通讯协议
        {procesID_BanYiTong_Chg_V1_2, CANBaud250k, initialize_Banyitong, Task_process_Banyitong},          // BanYiTong_Chg_V1.2        合肥搬易通BMS-CAN2.0A通讯协议V1.2_充电
        {procesID_ADY_V1_1, CANBaud250k, initialize_ADPower, Task_process_ADPower, NULL},                  // ADY_V1.1                  ADYPower充电机CAN通讯协议V1.1
        {procesID_ShiNeng, CANBaud250k, initialize_ShiNeng, Task_process_ShiNeng},                         // ShiNeng                   施能BMS通讯协议 V3.2版本（龙工）
        {procesID_CAN2_0C, CANBaud250k, initialize_CAN2_0C_B, Task_process_CAN2_0C_B, NULL},               // CAN2.0C                   充电CAN2.0C协议
        {procesID_TieCheng_Soc, CANBaud250k, initialize_TieCheng, Task_process_TieCheng, NULL},            // TieCheng_Soc              充电协议（增加SOC）
        {procesID_LongGong_Chg, CANBaud250k, initialize_LongGong_Chg, Task_process_LongGong_Chg, NULL},    // LongGong_Chg              发优旦技术协议（龙工充电协议）
        {procesID_XuGong, CANBaud250k, initialize_XuGong, Task_process_XuGong, NULL},                      // XuGong_20190821           1_1_徐工叉车用锂电池需求资料（充电协议）
        {procesID_HaoSheng, CANBaud250k, initialize_HAOSHENG, Task_process_ADPower, NULL},                 // HaoSheng                  豪盛BMS通信协议
        //        {procesID_YiJiaTong_QianTuo, CANBaud250k, initialize_EIKTO, process_EIKTO, NULL},                 // YiJiaTong_QianTuo         安徽益佳通充电协议(增加加热模式)20190213+BMS-CAN2.0A 通信协议V1.02_20190428
        //        {procesID_YiJiaTong_QianTuo_ZOWELL, CANBaud250k, initialize_EIKTO, process_EIKTO, NULL},          // YiJiaTong_QianTuo_ZOWELL  安徽益佳通充电协议(增加加热模式)20190213+BMS-CAN2.0A 通信协议V1.02_20190428+ZOWELL电协议V2.0

        {procesID_LigaoV4_03, CANBaud125k, initialize_LigaoV4_03, Task_process_LigaoV4_03, NULL},
};
// 检查并切换充电协议
// 传入上一次的协议类型、和CAN ID、和CAN速率指针
int charger_protocol_check(Charge_handlerType **protocol, uint8_t *last_protocol_type, uint8_t *last_can_id, uint8_t *last_can_speed)
{
    // 当前的协议类型和CAN ID
    uint8_t protocol_type = 0;
    uint8_t CAN_CH = 0;
    uint8_t CAN_Speed = 0;
    uint8_t CANxFunction[3] = {0}; // CANx功能配置
    uint8_t CH_protocol[3] = {0};  // CANx充电协议配置
    uint8_t CANxSpeed[3] = {0};    // CANx波特率配置

    // 获取CAN配置
    if (Charger_test_enable)
    {
        protocol_type = Charger_protocol_test_sid;
        CAN_Speed = Charger_protocol_test_baud;
        CAN_CH = Charger_protocol_test_can_id;
    }
    else // SIG_TYPE_UINT8
    {
        get_signals_value(SID_CAN0_CFG, CANxFunction, 3);             // CAN功能类型
        get_signals_value(SID_CAN0_CHG_PROTOCOL_CFG, CH_protocol, 3); // 充电协议名称
        get_signals_value(SID_CAN0_RBAUD_THE, CANxSpeed, 3);          // CAN速度枚举

        for (uint8_t i = 0; i < 3; i++) // CAN_FUNCTION_t
        {
            if (CANxFunction[i] == CAN_FAST_CHARGE || CANxFunction[i] == CAN_CHARGE_ALL)
            {
                CAN_CH = i;
                CAN_Speed = CANxSpeed[i];
                protocol_type = CH_protocol[i]; // 协议类型
                break;
            }
        }
    }

    // 检查协议类型和CAN波特率的有效性
    if (protocol_type >= procesID_MAX_ID || protocol_type < 0 || CAN_Speed >= CANBaudNum || CAN_Speed < 0)
    {
        return -1; // 协议类型无效
    }
    // 检查CAN波特率的有效性
    if (CAN_Speed >= CANBaudNum || CAN_Speed < 0)
    {
        return -1; // CAN波特率无效
    }
    // 检查CAN通道的有效性
    if (CAN_CH >= can_max || CAN_CH < can_0)
    {
        return -1; // CAN通道无效
    }

    // 检测协议类型、CAN通道、速率是否发生变化
    if (protocol_type != *last_protocol_type || CAN_CH != *last_can_id || CAN_Speed != *last_can_speed)
    {
        // 协议类型或CAN ID发生变化，查找新的协议类型
        for (uint8_t protocol_index = 0; protocol_index < (sizeof(protocol_list) / sizeof(protocol_list[0])); protocol_index++)
        {
            // 查找匹配的协议类型
            if (protocol_list[protocol_index].processID == protocol_type)
            {
                // 找到匹配的协议，更新协议指针
                *protocol = &protocol_list[protocol_index];
                break;
            }
        }

        // 如果没有找到匹配的协议，返回错误
        if (*protocol == NULL)
            return -1;

        // 更新协议的波特率和CAN ID
        (*protocol)->baud = CAN_Speed;

        // 根据CAN ID初始化CAN控制器
        switch (CAN_CH)
        {
        case can_0:
            DCCH_CAN = SGCC_CAN0_P;
            NVIC_SetPriority(CAN0_IRQn, 0x01);
            NVIC_EnableIRQ(CAN0_IRQn);
            CAN0_InitPad();
            CANX_Init(DCCH_CAN, (*protocol)->baud);

            break;
        case can_1:
            DCCH_CAN = SGCC_CAN1_P;
            NVIC_SetPriority(CAN1_IRQn, 0x01);
            NVIC_EnableIRQ(CAN1_IRQn);
            CAN1_InitPad();
            CANX_Init(DCCH_CAN, (*protocol)->baud);
            break;
        case can_2:
            DCCH_CAN = SGCC_CAN2_P;
            NVIC_SetPriority(CAN2_IRQn, 0x01);
            NVIC_EnableIRQ(CAN2_IRQn);
            CAN2_InitPad();
            CANX_Init(DCCH_CAN, (*protocol)->baud);
            break;
        default:
            return -1; // 无效的CAN通道
        }

        CAN_InitRingBuffer(&CAN_DCCH_Recv_Queue, CAN_DCCH_ReceFrame, sizeof(CAN_DCCH_ReceFrame) / sizeof(CAN_FrameInfoTypeDef));

        // 更新上一次的协议类型和CAN ID
        *last_protocol_type = protocol_type;
        *last_can_id = CAN_CH;
        *last_can_speed = CAN_Speed;
    }

    // 操作成功，返回0
    return 0;
}

// 功能：充电协议的周期性任务
void Task_charge_protocol(void)
{
    uint16_t xMS_TIME = 3000U;
    static Charge_handlerType *protocol = NULL;
    static uint8_t last_protocol_type = 0;
    static uint8_t last_can_id = 0;
    static uint8_t last_can_speed = 0;
    static uint32_t lastTimeTick = 0;
    static uint32_t time_send = 0;

    CAN_FrameInfoTypeDef msg;
    uint32_t timeNow = 0;
    timeNow = get_sys_tick();

    // 检查是否需要执行完整的协议检查流程
    if ((protocol == NULL) || ((timeNow - lastTimeTick) >= xMS_TIME)) // 每3秒检查一次
    {
        lastTimeTick = timeNow;

        if (charger_protocol_check(&protocol, &last_protocol_type, &last_can_id, &last_can_speed) == 0)
        {
            // 调用协议的初始化函数
            protocol->processInit();
        }
    }

    // 执行协议处理
    if (!protocol)
    {
        return;
    }
    if(protocol->processID != procesID_GB)
    {
        task_AssistPowermonitor_OBC();
    }
    protocol->processTask();

    //    if (timeNow - time_send > 10) // 10ms
    //    {
    //        time_send = timeNow;
    //        if (!CAN_queueOut(&CAN_Dcch_Send_Queue, &msg))
    //        {
    //            if (protocol->processSend)
    //            {
    //                protocol->processSend(msg);
    //            }
    //        }
    //    }
}

APP_TASK_DEFINE(Task_charge_protocol, "10");


/*
功能总结：
1. 周期性监测：每50ms检查一次DCC快充辅助电源状态
2. 状态防抖：要求新状态持续3个周期（150ms）才认为有效
3. 状态更新：当状态变化且稳定后，更新信号值
4. 电压设置：根据状态设置电压值（0或12000mV）
5. 信号输出：将更新后的电压值通过set_signals_value函数输出

主要步骤：
1. 时间检查：确保每50ms执行一次
2. 获取状态：调用GetAssistPowermonitor_Statu_DCC()获取当前状态
3. 状态比较：与上一状态比较，如不同则重置计数器
4. 状态计数：相同状态累计到3次时认为稳定
5. 信号更新：状态稳定且与上次不同时，更新输出信号

该函数通过定期检测和防抖机制，确保DCC快充辅助电源状态的准确监测和及时更新。
*/

// 监测OBC 慢充辅助电源
void task_AssistPowermonitor_OBC(void)
{
    // 50 ms获取一次状态，当状态变化时，则需要该状态持续三个周期再更新输出状态
    uint16_t xMS_TIME = 50;

    uint32_t timeNow_Xms = 0;
    uint16_t temp = 0;
    static uint32_t time_pre_Xms = 0;
    static boolean_T tempState_OBC = false;
    static boolean_T currentState_OBC = false;
    static boolean_T lastState_OBC = false;
    static uint8_t stateCounter_OBC = 0;
 

    timeNow_Xms = get_sys_tick();

    if (timeNow_Xms - time_pre_Xms < xMS_TIME)
    {
        return;
    }
    time_pre_Xms = timeNow_Xms;

    tempState_OBC = GetAssistPowermonitor_Statu_OBC();

    if (tempState_OBC != currentState_OBC)
    {
        stateCounter_OBC = 0;
        currentState_OBC = tempState_OBC;
    }
    else
    {
        if (stateCounter_OBC < 2)
        {
            stateCounter_OBC++;
        }
        else if (currentState_OBC != lastState_OBC)
        {
            lastState_OBC = currentState_OBC;
            temp = (lastState_OBC ? 0 : 12000);
            // 处理OBC 12V电源监控逻辑
            (void)set_signals_value(SIG_SCHG_AUXILIARY_VOLTAGE, &temp, 1);
            uint8_t temp1 = 0;
            if(temp == 0)
            {
                temp1 = 1;
                get_signals_value(SID_OBC_CHG_ONLINE, &temp1, 1);
       
            }
            else
            {
                temp1 = 0;
                set_signals_value(SID_OBC_CHG_ONLINE, &temp1, 1);
            }
        }
    }
}
