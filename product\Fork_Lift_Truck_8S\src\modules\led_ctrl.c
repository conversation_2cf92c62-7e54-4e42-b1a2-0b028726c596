#include "type.h"
#include "board_config.h"
#include "board.h"
#include "led_ctrl.h"
#include "hal_os.h"
#if defined(PROGRAM_TYPE_APP)
#include "battery_dev.h"
#endif


static void led_ctrl_task(void)
{
    unsigned int tick;
    static unsigned int tick_save;
	
    tick = get_sys_tick();
	if(tick-tick_save <1000)
	{	
		return;
	}
	tick_save = get_sys_tick();
	
 
}

APP_TASK_DEFINE(led_ctrl_task,"1");

