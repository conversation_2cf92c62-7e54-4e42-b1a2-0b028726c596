#include "drv_led_SCM63X.h"
#include "hal_os.h"
#include "SCM630_board.h"
#include "type.h"

//extern unsigned int get_sys_tick(void);



uint32_t LedTimeOut=250 ; //250ms
int LED_BLink_taskInit(void)
{
	LED_Init();
	return APP_OK;
}
APP_INIT_DEFINE(LED_BLink_taskInit,"10");

void LED_BLink_task(void)
{
	static uint32_t time_pre= 0;
	uint32_t timeNow=0;
	timeNow=get_sys_tick();
	if(timeNow-time_pre<250)
	{
		return;
	}
	time_pre=timeNow;
	LED_task();
}

APP_TASK_DEFINE(LED_BLink_task,"100");



