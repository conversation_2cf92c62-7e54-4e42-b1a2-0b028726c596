
#include "n32g45x.h"
#include "n32g45x_rtc.h"
#include "board_config.h"
#include "hal_os.h"
#include "battery_dev.h"
#include <stdio.h>
#include <stdint.h>
#include <type.h>


#ifdef RTC_WK_ENABLE

/** @addtogroup RTC_Calendar
 * @{
 */
RTC_DateType RTC_DateStructure;
RTC_DateType RTC_DateDefault;
RTC_TimeType RTC_TimeStructure;
RTC_TimeType RTC_TimeDefault;
RTC_InitType RTC_InitStructure;
RTC_AlarmType RTC_AlarmStructure;
RTC_AlarmType RTC_AlarmDefault;
uint32_t SynchPrediv, AsynchPrediv;

/*
 RTC_GetAlarm(RTC_FORMAT_BIN, RTC_A_ALARM, &RTC_AlarmStructure);
 RTC_GetDate(RTC_FORMAT_BIN, &RTC_DateStructure);
 RTC_GetTime(RTC_FORMAT_BIN, &RTC_TimeStructure);
*/
// test
void RTC_DateAndTimeDefaultVale(void)
{ // Date
    RTC_DateDefault.WeekDay = 0;
    RTC_DateDefault.Date    = 0;
    RTC_DateDefault.Month   = 0;
    RTC_DateDefault.Year    = 0;
    // Time
    RTC_TimeDefault.H12     = 0;
    RTC_TimeDefault.Hours   = 0;
    RTC_TimeDefault.Minutes = 0;
    RTC_TimeDefault.Seconds = 0;

    // Alarm
    RTC_AlarmDefault.AlarmTime.H12     = 0;
    RTC_AlarmDefault.AlarmTime.Hours   = 0;
    RTC_AlarmDefault.AlarmTime.Minutes = 0;
    RTC_AlarmDefault.AlarmTime.Seconds = 30;
    RTC_AlarmDefault.DateWeekMode      = 0;
    // RTC_AlarmDefault.DateWeekValue    = 0x31;
    // RTC_AlarmDefault.AlarmMask = RTC_ALARMMASK_MINUTES;
}

/**
 * @brief  RTC alarm regulate with the default value.
 */
ErrorStatus RTC_AlarmRegulate(uint32_t RTC_Alarm)
{
    uint32_t tmp_hh = 0xFF, tmp_mm = 0xFF, tmp_ss = 0xFF;

    // assert_param(IS_AlarmAB_VALUE(temp->temp_AlarmABSel));
    // assert_param(IS_AlarmDateWeekDaySel_VALUE(temp->temp_AlarmDateWeekDaySel));

    /* Disable the AlarmX */
    RTC_EnableAlarm(RTC_Alarm, DISABLE);

//    printf("\n\r //==============Alarm X Settings================// \n\r");
    RTC_AlarmStructure.AlarmTime.H12 = RTC_AM_H12;
    RTC_TimeStructure.H12            = RTC_AM_H12;

  //  printf("\n\r Please Set Alarm Hours \n\r");
    tmp_hh = RTC_AlarmDefault.AlarmTime.Hours;
    if (tmp_hh == 0xff)
    {
        return ERROR;
    }
    else
    {
        RTC_AlarmStructure.AlarmTime.Hours = tmp_hh;
    }
  //  printf(": %0.2d\n\r", tmp_hh);

  //  printf("\n\r Please Set Alarm Minutes \n\r");
    tmp_mm = RTC_AlarmDefault.AlarmTime.Minutes;
    if (tmp_mm == 0xff)
    {
        return ERROR;
    }
    else
    {
        RTC_AlarmStructure.AlarmTime.Minutes = tmp_mm;
    }
  //  printf(": %0.2d\n\r", tmp_mm);

  //  printf("\n\r Please Set Alarm Seconds \n\r");
    tmp_ss = RTC_AlarmDefault.AlarmTime.Seconds;
    if (tmp_ss == 0xff)
    {
        return ERROR;
    }
    else
    {
        RTC_AlarmStructure.AlarmTime.Seconds = tmp_ss;
    }
 //   printf(": %0.2d\n\r", tmp_ss);

    /* Set the Alarm X */
    RTC_AlarmStructure.DateWeekValue = 0x31;

    RTC_AlarmStructure.DateWeekMode = RTC_AlarmDefault.DateWeekMode;

    // RTC_AlarmStructure.AlarmMask = RTC_ALARMMASK_WEEKDAY | RTC_ALARMMASK_HOURS | RTC_ALARMMASK_SECONDS;
    RTC_AlarmStructure.AlarmMask = RTC_ALARMMASK_WEEKDAY | RTC_ALARMMASK_HOURS | RTC_ALARMMASK_MINUTES;
    // RTC_AlarmStructure.AlarmMask = RTC_ALARMMASK_WEEKDAY;

    /* Configure the RTC Alarm A register */
    RTC_SetAlarm(RTC_FORMAT_BIN, RTC_Alarm, &RTC_AlarmStructure);
 //   printf("\n\r>> !! RTC Set Alarm_X success. !! <<\n\r");

    if (RTC_Alarm == RTC_A_ALARM)
    {
        /* Enable the RTC Alarm A Interrupt */
        RTC_ConfigInt(RTC_INT_ALRA, ENABLE);
      
    }
    else
    {
        /* Enable the RTC Alarm B Interrupt */
        RTC_ConfigInt(RTC_INT_ALRB, ENABLE);
      
    }
    /* Enable the alarm   */
    RTC_EnableAlarm(RTC_Alarm, ENABLE);
	return SUCCESS;
}
/**
 * @brief  RTC date regulate with the default value.
 */
ErrorStatus RTC_DateRegulate(void)
{
    uint32_t tmp_hh = 0xFF, tmp_mm = 0xFF, tmp_ss = 0xFF;
   // log_info("\n\r //=============Date Settings================// \n\r");

   // log_info("\n\r Please Set WeekDay (01-07) \n\r");
    tmp_hh = RTC_DateDefault.WeekDay;
    if (tmp_hh == 0xff)
    {
        return ERROR;
    }
    else
    {
        RTC_DateStructure.WeekDay = tmp_hh;
    }
 

    tmp_hh = 0xFF;
 
    tmp_hh = RTC_DateDefault.Date;
    if (tmp_hh == 0xff)
    {
        return ERROR;
    }
    else
    {
        RTC_DateStructure.Date = tmp_hh;
    }
 
    tmp_mm = RTC_DateDefault.Month;
    if (tmp_mm == 0xff)
    {
        return ERROR;
    }
    else
    {
        RTC_DateStructure.Month = tmp_mm;
    }
 
    tmp_ss = RTC_DateDefault.Year;
    if (tmp_ss == 0xff)
    {
        return ERROR;
    }
    else
    {
        RTC_DateStructure.Year = tmp_ss;
    }

    /* Configure the RTC date register */
    if (RTC_SetDate(RTC_FORMAT_BIN, &RTC_DateStructure) == ERROR)
    {
        return ERROR;
    }
    else
    {
 
        return SUCCESS;
    }
}
/**
 * @brief  RTC time regulate with the default value.
 */
ErrorStatus RTC_TimeRegulate(void)
{
    uint32_t tmp_hh = 0xFF, tmp_mm = 0xFF, tmp_ss = 0xFF;
 
    RTC_TimeStructure.H12 = RTC_TimeDefault.H12;
 
    tmp_hh = RTC_TimeDefault.Hours;
    if (tmp_hh == 0xff)
    {
        return ERROR;
    }
    else
    {
        RTC_TimeStructure.Hours = tmp_hh;
    }
 
    tmp_mm = RTC_TimeDefault.Minutes;
    if (tmp_mm == 0xff)
    {
        return ERROR;
    }
    else
    {
        RTC_TimeStructure.Minutes = tmp_mm;
    }
 
    tmp_ss = RTC_TimeDefault.Seconds;
    if (tmp_ss == 0xff)
    {
        return ERROR;
    }
    else
    {
        RTC_TimeStructure.Seconds = tmp_ss;
    }
 
    /* Configure the RTC time register */
    if (RTC_ConfigTime(RTC_FORMAT_BIN, &RTC_TimeStructure) == ERROR)
    {
        return ERROR;
    }
    else
    {
        return SUCCESS;
    }
}

/**
 * @brief  RTC prescaler config.
 */
static void RTC_PrescalerConfig(void)
{
    /* Configure the RTC data register and RTC prescaler */
    RTC_InitStructure.RTC_AsynchPrediv = AsynchPrediv;
    RTC_InitStructure.RTC_SynchPrediv  = SynchPrediv;
    RTC_InitStructure.RTC_HourFormat   = RTC_24HOUR_FORMAT;

    /* Check on RTC init */
    if (RTC_Init(&RTC_InitStructure) == ERROR)
    {
    }
}



void RTC_CLKSourceConfig(uint8_t ClkSrc, uint8_t FirstLastCfg, uint8_t RstBKP)
{
    /* Enable the PWR clock */
   RCC_EnableAPB1PeriphClk(RCC_APB1_PERIPH_PWR | RCC_APB1_PERIPH_BKP, ENABLE);
 
    RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
    /* Allow access to RTC */
    PWR_BackupAccessEnable(ENABLE);

	PWR->CTRL |= 0x01<<3;// 
    if (RstBKP == 1)
    {
        BKP_DeInit();
    }
    /* Disable RTC clock */
    RCC_EnableRtcClk(DISABLE);

    if (ClkSrc == 0x01)
    {
        if (FirstLastCfg == 0)
        {
            /* Enable HSE */
            RCC_EnableLsi(DISABLE);
            RCC_ConfigHse(RCC_HSE_ENABLE);
            while (RCC_WaitHseStable() == ERROR)
            {
            }

            RCC_ConfigRtcClk(RCC_RTCCLK_SRC_HSE_DIV128);
        }
        else
        {
            RCC_EnableLsi(DISABLE);
            RCC_ConfigRtcClk(RCC_RTCCLK_SRC_HSE_DIV128);

            /* Enable HSE */
            RCC_ConfigHse(RCC_HSE_ENABLE);

            while (RCC_WaitHseStable() == ERROR)
            {
            }
        }

        SynchPrediv  = 0x1E8; // 8M/128 = 62.5KHz
        AsynchPrediv = 0x7F;  // value range: 0-7F
    }
    else if (ClkSrc == 0x02)
    {
        if (FirstLastCfg == 0)
        {
            /* Enable the LSE OSC32_IN PC14 */
            RCC_EnableLsi(DISABLE); // LSI is turned off here to ensure that only one clock is turned on

#if (_TEST_LSE_BYPASS_)
            RCC_ConfigLse(RCC_LSE_BYPASS);
#else
            RCC_ConfigLse(RCC_LSE_ENABLE);
#endif

            while (RCC_GetFlagStatus(RCC_FLAG_LSERD) == RESET)
            {
            }

            RCC_ConfigRtcClk(RCC_RTCCLK_SRC_LSE);
        }
        else
        {
            /* Enable the LSE OSC32_IN PC14 */
            RCC_EnableLsi(DISABLE);
            RCC_ConfigRtcClk(RCC_RTCCLK_SRC_LSE);

#if (_TEST_LSE_BYPASS_)
            RCC_ConfigLse(RCC_LSE_BYPASS);
#else
            RCC_ConfigLse(RCC_LSE_ENABLE);
#endif

            while (RCC_GetFlagStatus(RCC_FLAG_LSERD) == RESET)
            {
            }
        }

        SynchPrediv  = 0xFF; // 32.768KHz
        AsynchPrediv = 0x7F; // value range: 0-7F
    }
    else if (ClkSrc == 0x03)
    {
        if (FirstLastCfg == 0)
        {
            /* Enable the LSI OSC */
            RCC_EnableLsi(ENABLE);

            while (RCC_GetFlagStatus(RCC_FLAG_LSIRD) == RESET)
            {
            }

            RCC_ConfigRtcClk(RCC_RTCCLK_SRC_LSI);
        }
        else
        {
            RCC_ConfigRtcClk(RCC_RTCCLK_SRC_LSI);

            /* Enable the LSI OSC */
            RCC_EnableLsi(ENABLE);

            while (RCC_GetFlagStatus(RCC_FLAG_LSIRD) == RESET)
            {
            }
        }

        SynchPrediv  = 0x136; // 39.64928KHz
        AsynchPrediv = 0x7F;  // value range: 0-7F
    }
 

    /* Enable the RTC Clock */
    RCC_EnableRtcClk(ENABLE);
    RTC_WaitForSynchro();
}

/**
 * @brief  Wake up clock config.
 */
void WakeUpClockSelect(uint8_t WKUPClkSrcSel)
{
    /* Configure the RTC WakeUp Clock source: CK_SPRE (1Hz) */
    if (WKUPClkSrcSel == 0x01)
        RTC_ConfigWakeUpClock(RTC_WKUPCLK_RTCCLK_DIV16);
    else if (WKUPClkSrcSel == 0x02)
        RTC_ConfigWakeUpClock(RTC_WKUPCLK_RTCCLK_DIV8);
    else if (WKUPClkSrcSel == 0x03)
        RTC_ConfigWakeUpClock(RTC_WKUPCLK_RTCCLK_DIV4);
    else if (WKUPClkSrcSel == 0x04)
        RTC_ConfigWakeUpClock(RTC_WKUPCLK_RTCCLK_DIV2);
    else if (WKUPClkSrcSel == 0x05)
        RTC_ConfigWakeUpClock(RTC_WKUPCLK_CK_SPRE_16BITS);
}
/**
 * @brief  Config RTC wake up Interrupt.
 */
void EXTI20_RTCWKUP_Configuration(FunctionalState Cmd)
{
    EXTI_InitType EXTI_InitStructure;
    NVIC_InitType NVIC_InitStructure;

    EXTI_ClrITPendBit(EXTI_LINE20);
    EXTI_InitStructure.EXTI_Line = EXTI_LINE20;
#ifdef __TEST_SEVONPEND_WFE_NVIC_DIS__
    EXTI_InitStructure.EXTI_Mode = EXTI_Mode_Event;
#else
    EXTI_InitStructure.EXTI_Mode = EXTI_Mode_Interrupt;
#endif
    EXTI_InitStructure.EXTI_Trigger = EXTI_Trigger_Rising;
    EXTI_InitStructure.EXTI_LineCmd = ENABLE;
    EXTI_InitPeripheral(&EXTI_InitStructure);

    /* Enable the RTC WakeUp Interrupt */
    NVIC_InitStructure.NVIC_IRQChannel                   = RTC_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority        = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd                = Cmd;
    NVIC_Init(&NVIC_InitStructure);
}
/**
 * @brief  Main program.
 */
int rtc_init(void)
{
	SettingPara_t * para = (SettingPara_t *)SETTING_PARA_SAVE_RAM_ADDR;	
    /* RTC date time alarm default value*/
	if(	para->flag_rtc_bkp != 1)
	{
		para->flag_rtc_bkp = 1;
		
		RTC_DateAndTimeDefaultVale();

		/* RTC clock source select 1:HSE/128 2:LSE 3:LSI*/
		RTC_CLKSourceConfig(3, 0, 1);
		RTC_PrescalerConfig();
		
		/* RTC date time and alarm regulate*/
		RTC_DateRegulate();
		RTC_TimeRegulate();
		
	}

 
    /* wake up clock select */
    WakeUpClockSelect(5);
    /* wake up timer value */
    RTC_SetWakeUpCounter(10);	
	
	EXTI20_RTCWKUP_Configuration(ENABLE);
	/* Enable the RTC Wakeup Interrupt */
	RTC_ConfigInt(RTC_INT_WUT, ENABLE);
	//RTC_EnableWakeUp(DISABLE);
	RTC_EnableWakeUp(DISABLE);
	RCC_EnableAPB1PeriphClk(RCC_APB1_PERIPH_PWR,DISABLE); 	

	//RCC_EnableAPB1PeriphClk(RCC_APB1_PERIPH_PWR, ENABLE);
	return 0;
}
APP_INIT_DEFINE(rtc_init,"10");
 



void proc_SID_RTC_ALARM_SELECT(unsigned char sig_value)
{
	if(sig_value==0)
	{
		RTC_EnableWakeUp(DISABLE);	
	}
	else
	{
	    /* RTC date time alarm default value*/
		RTC_DateAndTimeDefaultVale();

		/* RTC date time and alarm regulate*/
		RTC_DateRegulate();
		RTC_TimeRegulate();
	 
		/* wake up clock select */
		WakeUpClockSelect(5);
		/* wake up timer value */

//		RTC_SetWakeUpCounter((unsigned int)sig_value*10);
//		RTC_SetWakeUpCounter((unsigned int)sig_value*60);
		RTC_SetWakeUpCounter((unsigned int)sig_value*60*10);	

		EXTI20_RTCWKUP_Configuration(ENABLE);
		/* Enable the RTC Wakeup Interrupt */
		RTC_ConfigInt(RTC_INT_WUT, ENABLE);
		RTC_EnableWakeUp(ENABLE);
	}
}
 
//RTC_TimeType sturc_time;
//RTC_DateType sturc_date;
//void get_time_task(void)
//{
//	unsigned int led_tick = 0;
//	static unsigned int current_tick_save = 0;
//	led_tick = get_sys_tick();

//	if(led_tick - current_tick_save > 1000)
//	{
//		current_tick_save = led_tick;
//		
//		RTC_GetTime(RTC_FORMAT_BIN,&sturc_time);
//		RTC_GetDate(RTC_FORMAT_BIN,&sturc_date); 
//		sturc_date.Year++;
//		RTC_SetDate(RTC_FORMAT_BIN,&sturc_date);
//	}
//}
//APP_TASK_DEFINE(get_time_task,"1");



#endif
