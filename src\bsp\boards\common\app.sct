#if (APP_ADDR_FLASH_BEGIN == APP_ADDR_BK_FLASH_BEGIN)
IROM (APP_ADDR_FLASH_BEGIN+APP_BIN_HEADER)  (SIZES_MAX_APP_FLASH-APP_BIN_HEADER-4) {
#else
IROM APP_ADDR_FLASH_BEGIN  (SIZES_MAX_APP_FLASH-APP_BIN_HEADER-4) {
#endif    
  APP_IROM +0   { 
   *.o (RESET, +First)
   *(InRoot$$Sections)
   .ANY (+RO)
  }
  INIT_IROM AlignExpr(+0,4) SORTTYPE Lexical {
   app.o (.app_init_start +FIRST)
   *.o (.app_init.0)
   *.o (.app_init.1)
   *.o (.app_init.2)
   *.o (.app_init.3)
   *.o (.app_init.4)
   *.o (.app_init.5)
   *.o (.app_init.6)
   *.o (.app_init.7)
   *.o (.app_init.8)
   *.o (.app_init.9)
  }
  INIT_IROM_OTHER +0 {
   *.o (.app_init.*)
   app.o (.app_init_end +LAST)
  }

  TASK_IROM AlignExpr(+0,4) SORTTYPE Lexical {
   app.o (.app_task_start +FIRST)
   *.o (.app_task.0)
   *.o (.app_task.1)
   *.o (.app_task.2)
   *.o (.app_task.3)
   *.o (.app_task.4)
   *.o (.app_task.5)
   *.o (.app_task.6)
   *.o (.app_task.7)
   *.o (.app_task.8)
   *.o (.app_task.9)
  }
  TASK_IROM_OTHER AlignExpr(+0,4) SORTTYPE Lexical {
   *.o (.app_task.*)
   app.o (.app_task_end +LAST)
  }

  RW_IRAM1 (ADDR_IRAM_BEGIN+SIZES_VTOR+SIZES_IRAM_RESERVE) (SIZES_MAX_IRAM-SIZES_VTOR-SIZES_IRAM_RESERVE)   {  
   .ANY (+RW +ZI)
  }
}

