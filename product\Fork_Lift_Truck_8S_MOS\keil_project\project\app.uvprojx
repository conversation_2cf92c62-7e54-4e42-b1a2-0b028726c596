<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>relese</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060750::V5.06 update 6 (build 750)::ARMCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>ARMCM4_FP</Device>
          <Vendor>ARM</Vendor>
          <PackID>ARM.CMSIS.5.4.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x00020000) IROM(0x00000000,0x00040000) CPUTYPE("Cortex-M4") FPU2 CLOCK(12000000) ESEL ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000)</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:ARMCM4_FP$Device\ARM\ARMCM4\Include\ARMCM4_FP.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:ARMCM4_FP$Device\ARM\SVD\ARMCM4.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\Objects_app\OUTPUT\</OutputDirectory>
          <OutputName>app</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Listing\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>1</RunUserProg2>
            <UserProg1Name>fromelf --bin -o "$<EMAIL>" "#L</UserProg1Name>
            <UserProg2Name>make_upgrade.bat  V1.0.01</UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments>  -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>0</useUlib>
            <EndSel>1</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x40000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x40000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>0</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>1</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls>--c99</MiscControls>
              <Define>PROGRAM_TYPE_APP,APP_PRINTF_OUT,__FPU_PRESENT</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\..\..\src\bsp\boards\ForkLiftTruck8S;..\..\..\..\src\bsp\SCM630_Firmware_Library\CMSIS\CMSIS;..\..\..\..\src\bsp\SCM630_Firmware_Library\CMSIS\Startup;..\..\..\..\src\bsp\SCM630_Firmware_Library\standard_peripheral\include;..\..\..\..\open_src\lzma1900\C;..\..\..\..\src\include;..\..\src\battery;..\..\src;..\..\..\..\src\bsp\boards\common;..\..\..\..\src\modules;..\..\..\..\src\include;..\..\..\..\src\modules\core;..\..\..\..\src\modules\host_comm;..\..\..\..\src\bsp\cmsis;..\..\..\..\src\bsp\driver;..\..\src\battery;..\..\src\modules\DCch;..\..\src\modules\DCch\J1939;..\..\src\battery;..\..\src\modules\vehicle_protocol;..\..\..\..\src\bsp\boards\ForkLiftTruck8S\cm_backtrace</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <uClangAs>0</uClangAs>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile>..\..\..\..\src\bsp\boards\ForkLiftTruck8S\app.sct</ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc>--diag_suppress=L6314</Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>app</GroupName>
          <Files>
            <File>
              <FileName>app.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\app.c</FilePath>
            </File>
            <File>
              <FileName>app_debug.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\src\app_debug.c</FilePath>
            </File>
            <File>
              <FileName>program_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\program_common.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>modules</GroupName>
          <Files>
            <File>
              <FileName>control_logic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\src\modules\control_logic.c</FilePath>
            </File>
            <File>
              <FileName>product.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\src\product.c</FilePath>
            </File>
            <File>
              <FileName>battery_dev.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\src\battery\battery_dev.c</FilePath>
            </File>
            <File>
              <FileName>slave_unit.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\src\modules\slave_unit.c</FilePath>
            </File>
            <File>
              <FileName>sox.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\src\modules\sox.c</FilePath>
            </File>
            <File>
              <FileName>math.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\modules\math.c</FilePath>
            </File>
            <File>
              <FileName>crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\lib\crc.c</FilePath>
            </File>
            <File>
              <FileName>led_ctrl_simply.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\src\modules\led_ctrl_simply.c</FilePath>
            </File>
            <File>
              <FileName>Tools.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\src\modules\DCch\Tools.c</FilePath>
            </File>
            <File>
              <FileName>vehicle_protocol.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\src\modules\vehicle_protocol\vehicle_protocol.c</FilePath>
            </File>
            <File>
              <FileName>balance.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\src\modules\balance.c</FilePath>
            </File>
            <File>
              <FileName>adc_SCM63X.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\src\modules\adc_SCM63X.c</FilePath>
            </File>
            <File>
              <FileName>voltage_temperature_arbitrate.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\src\modules\voltage_temperature_arbitrate.c</FilePath>
            </File>
            <File>
              <FileName>timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\modules\hal_os\timer.c</FilePath>
            </File>
            <File>
              <FileName>afe.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\src\modules\afe\afe.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>bsp_board</GroupName>
          <Files>
            <File>
              <FileName>board.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\boards\ForkLiftTruck8S\board.c</FilePath>
            </File>
            <File>
              <FileName>SCM630_board.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\boards\common\SCM630_board.c</FilePath>
            </File>
            <File>
              <FileName>canbus_protocal.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\modules\host_comm\canbus_protocal.c</FilePath>
            </File>
            <File>
              <FileName>EIKTO_canbus.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\modules\host_comm\EIKTO_canbus.c</FilePath>
            </File>
            <File>
              <FileName>EIKTO_protocol.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\modules\host_comm\EIKTO_protocol.c</FilePath>
            </File>
            <File>
              <FileName>upgrade_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\modules\host_comm\upgrade_common.c</FilePath>
            </File>
            <File>
              <FileName>cm_backtrace.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\boards\ForkLiftTruck8S\cm_backtrace\cm_backtrace.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>bsp_driver</GroupName>
          <Files>
            <File>
              <FileName>startup_SCM63X.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\..\..\..\src\bsp\SCM630_Firmware_Library\CMSIS\Startup\startup_SCM63X.s</FilePath>
            </File>
            <File>
              <FileName>flash_SCM63X.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\driver\flash_SCM63X.c</FilePath>
            </File>
            <File>
              <FileName>can_SCM63X.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\driver\can_SCM63X.c</FilePath>
            </File>
            <File>
              <FileName>uart_scm630.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\driver\uart_scm630.c</FilePath>
            </File>
            <File>
              <FileName>drv_uart_scm63X.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\driver\drv_uart_scm63X.c</FilePath>
            </File>
            <File>
              <FileName>drv_gpio_SCM63X.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\driver\drv_gpio_SCM63X.c</FilePath>
            </File>
            <File>
              <FileName>drv_ring_queue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\driver\drv_ring_queue.c</FilePath>
            </File>
            <File>
              <FileName>hw_agus_app.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\driver\hw_agus_app.c</FilePath>
            </File>
            <File>
              <FileName>hw_agus_event.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\driver\hw_agus_event.c</FilePath>
            </File>
            <File>
              <FileName>hw_agus_heap.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\driver\hw_agus_heap.c</FilePath>
            </File>
            <File>
              <FileName>hw_agus_mem.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\driver\hw_agus_mem.c</FilePath>
            </File>
            <File>
              <FileName>hw_agus_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\driver\hw_agus_utils.c</FilePath>
            </File>
            <File>
              <FileName>hw_ec200_communication .c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\driver\hw_ec200_communication .c</FilePath>
            </File>
            <File>
              <FileName>hw_ec200_driver.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\driver\hw_ec200_driver.c</FilePath>
            </File>
            <File>
              <FileName>hw_ring_buffer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\driver\hw_ring_buffer.c</FilePath>
            </File>
            <File>
              <FileName>ntc_sensor.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\driver\ntc_sensor.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>hal_os</GroupName>
        </Group>
        <Group>
          <GroupName>lib</GroupName>
        </Group>
        <Group>
          <GroupName>scm630_lib</GroupName>
          <Files>
            <File>
              <FileName>misc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\SCM630_Firmware_Library\standard_peripheral\source\misc.c</FilePath>
            </File>
            <File>
              <FileName>SCM63X_7816.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\SCM630_Firmware_Library\standard_peripheral\source\SCM63X_7816.c</FilePath>
            </File>
            <File>
              <FileName>SCM63X_ADC.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\SCM630_Firmware_Library\standard_peripheral\source\SCM63X_ADC.c</FilePath>
            </File>
            <File>
              <FileName>SCM63X_AES.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\SCM630_Firmware_Library\standard_peripheral\source\SCM63X_AES.c</FilePath>
            </File>
            <File>
              <FileName>SCM63X_CAN.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\SCM630_Firmware_Library\standard_peripheral\source\SCM63X_CAN.c</FilePath>
            </File>
            <File>
              <FileName>SCM63X_CLK.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\SCM630_Firmware_Library\standard_peripheral\source\SCM63X_CLK.c</FilePath>
            </File>
            <File>
              <FileName>SCM63X_CRC.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\SCM630_Firmware_Library\standard_peripheral\source\SCM63X_CRC.c</FilePath>
            </File>
            <File>
              <FileName>SCM63X_DMA.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\SCM630_Firmware_Library\standard_peripheral\source\SCM63X_DMA.c</FilePath>
            </File>
            <File>
              <FileName>SCM63X_FLASH.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\SCM630_Firmware_Library\standard_peripheral\source\SCM63X_FLASH.c</FilePath>
            </File>
            <File>
              <FileName>SCM63X_GPIO.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\SCM630_Firmware_Library\standard_peripheral\source\SCM63X_GPIO.c</FilePath>
            </File>
            <File>
              <FileName>SCM63X_I2C.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\SCM630_Firmware_Library\standard_peripheral\source\SCM63X_I2C.c</FilePath>
            </File>
            <File>
              <FileName>SCM63X_IOCTRL.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\SCM630_Firmware_Library\standard_peripheral\source\SCM63X_IOCTRL.c</FilePath>
            </File>
            <File>
              <FileName>SCM63X_NFC.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\SCM630_Firmware_Library\standard_peripheral\source\SCM63X_NFC.c</FilePath>
            </File>
            <File>
              <FileName>SCM63X_PWM.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\SCM630_Firmware_Library\standard_peripheral\source\SCM63X_PWM.c</FilePath>
            </File>
            <File>
              <FileName>SCM63X_QSPI.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\SCM630_Firmware_Library\standard_peripheral\source\SCM63X_QSPI.c</FilePath>
            </File>
            <File>
              <FileName>SCM63X_RESET.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\SCM630_Firmware_Library\standard_peripheral\source\SCM63X_RESET.c</FilePath>
            </File>
            <File>
              <FileName>SCM63X_RTC.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\SCM630_Firmware_Library\standard_peripheral\source\SCM63X_RTC.c</FilePath>
            </File>
            <File>
              <FileName>SCM63X_SCU.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\SCM630_Firmware_Library\standard_peripheral\source\SCM63X_SCU.c</FilePath>
            </File>
            <File>
              <FileName>SCM63X_SM4.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\SCM630_Firmware_Library\standard_peripheral\source\SCM63X_SM4.c</FilePath>
            </File>
            <File>
              <FileName>SCM63X_SPI.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\SCM630_Firmware_Library\standard_peripheral\source\SCM63X_SPI.c</FilePath>
            </File>
            <File>
              <FileName>SCM63X_TIMER.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\SCM630_Firmware_Library\standard_peripheral\source\SCM63X_TIMER.c</FilePath>
            </File>
            <File>
              <FileName>SCM63X_UART.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\SCM630_Firmware_Library\standard_peripheral\source\SCM63X_UART.c</FilePath>
            </File>
            <File>
              <FileName>SCM63X_WDT.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\src\bsp\SCM630_Firmware_Library\standard_peripheral\source\SCM63X_WDT.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components/>
    <files>
      <file attr="config" category="header" name="Config\EventRecorderConf.h" version="1.1.0">
        <instance index="0" removed="1">RTE\Compiler\EventRecorderConf.h</instance>
        <component Cbundle="ARM Compiler" Cclass="Compiler" Cgroup="Event Recorder" Cvariant="DAP" Cvendor="Keil" Cversion="1.4.0" condition="Cortex-M Device"/>
        <package name="ARM_Compiler" schemaVersion="1.4.9" url="http://www.keil.com/pack/" vendor="Keil" version="1.6.1"/>
        <targetInfos/>
      </file>
    </files>
  </RTE>

</Project>
