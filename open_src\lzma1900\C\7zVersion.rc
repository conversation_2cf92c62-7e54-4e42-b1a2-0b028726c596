#define MY_VS_FFI_FILEFLAGSMASK  0x0000003FL
#define MY_VOS_NT_WINDOWS32  0x00040004L
#define MY_VOS_CE_WINDOWS32  0x00050004L

#define MY_VFT_APP  0x00000001L
#define MY_VFT_DLL  0x00000002L

// #include <WinVer.h>

#ifndef MY_VERSION
#include "7zVersion.h"
#endif

#define MY_VER MY_VER_MAJOR,MY_VER_MINOR,MY_VER_BUILD,0

#ifdef DEBUG
#define DBG_FL VS_FF_DEBUG
#else
#define DBG_FL 0
#endif

#define MY_VERSION_INFO(fileType, descr, intName, origName)  \
LANGUAGE 9, 1 \
1 VERSIONINFO \
  FILEVERSION MY_VER \
  PRODUCTVERSION MY_VER \
  FILEFLAGSMASK MY_VS_FFI_FILEFLAGSMASK \
  FILEFLAGS DBG_FL \
  FILEOS MY_VOS_NT_WINDOWS32 \
  FILETYPE fileType \
  FILESUBTYPE 0x0L \
BEGIN \
    BLOCK "StringFileInfo" \
    BEGIN  \
        BLOCK "040904b0" \
        BEGIN \
            VALUE "CompanyName", "Igor Pavlov" \
            VALUE "FileDescription", descr \
            VALUE "FileVersion", MY_VERSION  \
            VALUE "InternalName", intName \
            VALUE "LegalCopyright", MY_COPYRIGHT \
            VALUE "OriginalFilename", origName \
            VALUE "ProductName", "7-Zip" \
            VALUE "ProductVersion", MY_VERSION \
        END \
    END \
    BLOCK "VarFileInfo" \
    BEGIN \
        VALUE "Translation", 0x409, 1200 \
    END \
END

#define MY_VERSION_INFO_APP(descr, intName) MY_VERSION_INFO(MY_VFT_APP, descr, intName, intName ".exe")

#define MY_VERSION_INFO_DLL(descr, intName) MY_VERSION_INFO(MY_VFT_DLL, descr, intName, intName ".dll")
