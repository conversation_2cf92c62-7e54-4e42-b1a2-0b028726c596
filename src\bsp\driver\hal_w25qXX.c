#include "hal_w25qXX.h"
#include "stdarg.h"

FLASH_STATUS_REG_TypeDef g_flash_reg;

FLASH_ID_TypeDef flash_id[] = {
    {"W25X25", 0xEF3011},
    {"W25Q32D", 0xEF4016},
    {"W25Q16D", 0xEF4015},
    {"W25Q64D", 0xEF4017},
    {"W25Q128D", 0xEF4018},
};

uint8_t flash_read_status_reg(void)
{
    uint8_t result = 0xff;
    hal_w25qXX_start();
    hal_w25qXX_send_byte(FLASH_READ_STATUS_1_REG);
    result = hal_w25qXX_rec_byte();
    hal_w25qXX_stop();
    return result;
}

void flash_wait_ready(void)
{
    FLASH_STATUS_REG_TypeDef flash_reg;
    do
    {
        flash_reg.reg_val = flash_read_status_reg();
    } while (flash_reg.bits.BUSY);
}

void flash_write_status_reg(uint8_t reg_val)
{
    flash_wait_ready();
    flash_write_enable();
    hal_w25qXX_start();
    hal_w25qXX_send_byte(FLASH_WRITE_STATUS_REG);
    hal_w25qXX_send_byte(reg_val);
    hal_w25qXX_stop();
    flash_write_disable();
}

void flash_write_enable(void)
{
    hal_w25qXX_start();
    hal_w25qXX_send_byte(FLASH_WRITE_ENABLE);
    hal_w25qXX_stop();
}

void flash_write_disable(void)
{
    hal_w25qXX_start();
    hal_w25qXX_send_byte(FLASH_WRITE_DISABLE);
    hal_w25qXX_stop();
}

void flash_protect(uint8_t protect_opt)
{
    flash_write_status_reg(protect_opt);
}

void flash_write_byte(uint32_t addr, uint8_t val)
{
    flash_wait_ready();
    flash_write_enable();
    hal_w25qXX_start();
    hal_w25qXX_send_byte(FLASH_PAGE_PROGRAM);
    hal_w25qXX_send_byte((addr & 0xff0000) >> 16);
    hal_w25qXX_send_byte((addr & 0xff00) >> 8);
    hal_w25qXX_send_byte(addr);
    hal_w25qXX_send_byte(val);
    hal_w25qXX_stop();
}

uint8_t flash_read_byte(uint32_t addr)
{
    uint8_t result;
    flash_wait_ready();
    hal_w25qXX_start();
    hal_w25qXX_send_byte(FLASH_READ_DATA);
    hal_w25qXX_send_byte((addr & 0xff0000) >> 16);
    hal_w25qXX_send_byte((addr & 0xff00) >> 8);
    hal_w25qXX_send_byte(addr);
    result = hal_w25qXX_rec_byte();
    hal_w25qXX_stop();
    return result;
}

uint32_t flash_write_page(uint32_t addr,uint8_t *buf, uint32_t len)
{
	uint32_t w_len;
	uint16_t rem_bytes;
	uint16_t i;
	
	w_len = 0;
	while(len)
	{
		rem_bytes = FLASH_PAGE_BYTE_CNT - (addr &(FLASH_PAGE_BYTE_CNT-1));//计算当前页剩余空闲字节数
		if(rem_bytes>=len)
		{
			rem_bytes = len;//本次写入长度
		}
		flash_wait_ready();
		flash_write_enable();
		hal_w25qXX_start();	
		hal_w25qXX_send_byte(FLASH_PAGE_PROGRAM);
		hal_w25qXX_send_byte((addr&0xff0000)>>16);
		hal_w25qXX_send_byte((addr&0xff00)>>8);
		hal_w25qXX_send_byte(addr);
		for(i=0;i<rem_bytes;i++)
		{
			hal_w25qXX_send_byte(*(buf+i));
		}
		hal_w25qXX_stop();
		
		addr += rem_bytes;
		buf += rem_bytes;
		len -= rem_bytes;
		w_len += rem_bytes;
	}
	
	return w_len;
}

void flash_read_bytes(uint32_t addr, uint8_t *buf, uint32_t len)
{
    uint16_t i;
    flash_wait_ready();
    hal_w25qXX_start();
    hal_w25qXX_send_byte(FLASH_READ_DATA);
    hal_w25qXX_send_byte((addr & 0xff0000) >> 16);
    hal_w25qXX_send_byte((addr & 0xff00) >> 8);
    hal_w25qXX_send_byte(addr);
    for (i = 0; i < len; i++)
    {
        *(buf + i) = hal_w25qXX_rec_byte();
    }
    hal_w25qXX_stop();
}

void flash_erase(uint8_t cmd, ...)
{
    uint32_t addr;
    va_list arg_ptr;
    flash_wait_ready();
    flash_write_enable();
    hal_w25qXX_start();
    hal_w25qXX_send_byte(cmd);
    switch (cmd)
    {
    case FLASH_SECTOR_ERASE:
    case FLASH_BLOCK_32K_ERASE:
    case FLASH_BLOCK_64K_ERASE: {
        va_start(arg_ptr, cmd);
        addr = va_arg(arg_ptr, unsigned int);
        va_end(arg_ptr);
        hal_w25qXX_send_byte((addr & 0xff0000) >> 16);
        hal_w25qXX_send_byte((addr & 0xff00) >> 8);
        hal_w25qXX_send_byte(addr);
    }
    break;
    case FLASH_CHIP_ERASE:
        break;
    default:
        break;
    }
    hal_w25qXX_stop();
}

uint32_t flash_read_jedec_id(void)
{
    uint8_t i;
    uint32_t jedec_id = 0x00;
    flash_wait_ready();
    hal_w25qXX_start();
    hal_w25qXX_send_byte(FLASH_READ_JEDEC_ID);
    for (i = 3; i > 0; i--)
    {
        jedec_id <<= 8;
        jedec_id |= hal_w25qXX_rec_byte();
    }
    hal_w25qXX_stop();
    return jedec_id;
}

void flash_read_device_id(uint8_t *device_id)
{
    uint8_t i;
    flash_wait_ready();
    hal_w25qXX_start();
    hal_w25qXX_send_byte(FLASH_READ_UNIQUE_ID);
    for (i = 0; i < 4; i++)
    {
        hal_w25qXX_send_byte(0xFF);
    }
    for (i = 8; i > 0; i--)
    {
        device_id[i - 1] = hal_w25qXX_rec_byte();
    }
    hal_w25qXX_stop();
}


