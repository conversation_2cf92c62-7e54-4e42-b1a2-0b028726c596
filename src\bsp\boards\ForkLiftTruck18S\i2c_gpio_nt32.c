#include "type.h"
#include "drv_i2c.h"
#include "n32g45x.h"

#define I2C1_SCL_PORT   GPIOB
#define I2C1_SCL_PIN    GPIO_PIN_10
#define I2C1_SDA_PORT   GPIOB
#define I2C1_SDA_PIN    GPIO_PIN_11

#define I2C2_SCL_PORT   GPIOB
#define I2C2_SCL_PIN    GPIO_PIN_10
#define I2C2_SDA_PORT   GPIOB
#define I2C2_SDA_PIN    GPIO_PIN_11

//static const unsigned int s_delays_fast[] = {2,1}; //{T_LOW,T_HIGH}

//static const unsigned int s_delays_standard[] = {3,2};   //{T_LOW,T_HIGH}

static const unsigned int s_delays_slow[] = {6,5};   //{T_LOW,T_HIGH}

#include "i2c_gpio_nt32.inc"

const struct i2c_bitbang s_context[] = 
{
    {I2C_ID1,I2C1_SDA_PORT,I2C1_SCL_PORT,I2C1_SDA_PIN,I2C1_SCL_PIN,s_delays_slow},
    {I2C_ID2,I2C2_SDA_PORT,I2C2_SCL_PORT,I2C2_SDA_PIN,I2C2_SCL_PIN,s_delays_slow},
};

void drv_i2c_master_init(I2C_ID i2c_id, unsigned int max_baud_rate)
{
    (void)max_baud_rate;
    struct i2c_bitbang *context;
    if (I2C_ID1 == i2c_id)
    {
        context = (struct i2c_bitbang *)&s_context[0];
    }
    else if (I2C_ID2 == i2c_id)
    {
        context = (struct i2c_bitbang *)&s_context[1];
    }
    else
    {
        return;
    }
    i2c_set_sda(context,1);
    i2c_set_scl(context,1);
}

int drv_i2c_mem_read(I2C_ID i2c_id, unsigned char dev_addr, unsigned short mem_addr, 
    unsigned short mem_addr_size, unsigned char *pdata, unsigned char size)
{
    struct i2c_bitbang *context;
	if (I2C_ID1 == i2c_id)
	{
		context = (struct i2c_bitbang *)&s_context[0];
	}
	else if (I2C_ID2 == i2c_id)
	{
		context = (struct i2c_bitbang *)&s_context[1];
	}
	else
	{
		return APP_INVALID_PARA;
	}
    
    struct i2c_msg msg[2];
    unsigned char wdata[4];
    if (mem_addr_size==1)
    {
        wdata[0] = (unsigned char)(mem_addr&0xFF);
        wdata[1] = size;
        msg[0].len = 2;
    }
    else if (mem_addr_size==2)
    {
        wdata[0] = (unsigned char)(mem_addr&0xFF);
        wdata[1] = (unsigned char)(mem_addr>>8);
        wdata[2] = size;
        msg[0].len = 3;
    }
    else
    {
        return APP_ERROR;
    }
    msg[0].buf = wdata;
    msg[0].flags = I2C_MSG_WRITE;
    msg[1].buf = pdata;
    msg[1].len = size;
    msg[1].flags = I2C_MSG_RESTART | I2C_MSG_READ;
    if(APP_OK != i2c_bitbang_transfer(context,msg,2,dev_addr))
    {
        (void)i2c_bitbang_recover_bus(context);
        return APP_ERROR;
    }
    return APP_OK;
}

int drv_i2c_mem_write(I2C_ID i2c_id, unsigned char dev_addr, unsigned short mem_addr, 
    unsigned short mem_addr_size,unsigned char *pdata, unsigned char size)
{
    struct i2c_bitbang *context;
	if (I2C_ID1 == i2c_id)
	{
		context = (struct i2c_bitbang *)&s_context[0];
	}
	else if (I2C_ID2 == i2c_id)
	{
		context = (struct i2c_bitbang *)&s_context[1];
	}
	else
	{
		return APP_INVALID_PARA;
	}
    struct i2c_msg msg[2];
    unsigned char wdata[4];
    if (mem_addr_size==1)
    {
        wdata[0] = (unsigned char)(mem_addr&0xFF);
        wdata[1] = size;
        msg[0].len = 2;
    }
    else if (mem_addr_size==2)
    {
        wdata[0] = (unsigned char)(mem_addr&0xFF);
        wdata[1] = (unsigned char)(mem_addr>>8);
        wdata[2] = size;
        msg[0].len = 3;
    }
    else
    {
        return APP_ERROR;
    }
    msg[0].buf = wdata;
    msg[0].flags = I2C_MSG_WRITE;
    msg[1].buf = pdata;
    msg[1].len = size;
    msg[1].flags = I2C_MSG_WRITE;
    if(APP_OK != i2c_bitbang_transfer(context,msg,2,dev_addr))
    {
        (void)i2c_bitbang_recover_bus(context);
        return APP_ERROR;
    }
    return APP_OK;
}

int drv_twi_mem_read(I2C_ID i2c_id, unsigned char dev_addr, unsigned char mem_addr, 
    unsigned char *pdata, unsigned char size)
{
    struct i2c_bitbang *context;
	if (I2C_ID1 == i2c_id)
	{
		context = (struct i2c_bitbang *)&s_context[0];
	}
	else if (I2C_ID2 == i2c_id)
	{
		context = (struct i2c_bitbang *)&s_context[1];
	}
	else
	{
		return APP_INVALID_PARA;
	}
    unsigned char w_data[2];
    w_data[0] = mem_addr;
    w_data[1] = size;
    struct i2c_msg msg[2];
    msg[0].buf = w_data;
    msg[0].len = 2;
    msg[0].flags = I2C_MSG_WRITE;
    msg[1].buf = pdata;
    msg[1].len = size+1;
    msg[1].flags = I2C_MSG_RESTART | I2C_MSG_READ;
    if(APP_OK != i2c_bitbang_transfer(context,msg,2,dev_addr))
    {
        (void)i2c_bitbang_recover_bus(context);
        return APP_ERROR;
    }
    return APP_OK;
}

int drv_twi_mem_write(I2C_ID i2c_id, unsigned char dev_addr, unsigned char mem_addr, 
     unsigned char *pdata, unsigned char size)
{
    struct i2c_bitbang *context;
	if (I2C_ID1 == i2c_id)
	{
		context = (struct i2c_bitbang *)&s_context[0];
	}
	else if (I2C_ID2 == i2c_id)
	{
		context = (struct i2c_bitbang *)&s_context[1];
	}
	else
	{
		return APP_INVALID_PARA;
	}
    struct i2c_msg msg[2];
    msg[0].buf = (unsigned char*)&mem_addr;
    msg[0].len = 1;
    msg[0].flags = I2C_MSG_WRITE;
    msg[1].buf = pdata;
    msg[1].len = size+1;
    msg[1].flags = I2C_MSG_WRITE;
    if(APP_OK != i2c_bitbang_transfer(context,msg,2,dev_addr))
    {
        (void)i2c_bitbang_recover_bus(context);
        return APP_ERROR;
    }
    return APP_OK;
}

