
IROM BOOT_ADDR_FLASH_BEGIN  SIZES_MAX_BOOT_FLASH 
{    
  BOOT_IROM +0 
  {  
   *.o (RESET, +First)
   *(InRoot$$Sections)
   .ANY (+RO)
  }
  INIT_IROM AlignExpr(+0,4) SORTTYPE Lexical 
  {
   boot.o (.boot_init_start +FIRST)
   *.o (.boot_init.*)
  }
  INIT_IROM_END +0 
  {
   boot.o (.boot_init_end +FIRST)
  }
  TASK_IROM AlignExpr(+0,4) SORTTYPE Lexical 
  {
   boot.o (.boot_task_start +FIRST)
   *.o (.boot_task.*)
  }
  TASK_IROM_END +0 
  {
   boot.o (.boot_task_end +FIRST)
  }
  RW_IRAM1 (ADDR_IRAM_BEGIN+SIZES_VTOR+SIZES_IRAM_RESERVE) (SIZES_MAX_IRAM-SIZES_VTOR-SIZES_IRAM_RESERVE) {  
   .ANY (+RW +ZI)
  }
}

