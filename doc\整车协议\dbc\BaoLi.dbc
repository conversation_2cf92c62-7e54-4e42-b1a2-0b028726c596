VERSION ""


NS_ : 
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_
	BO_TX_BU_
	BA_DEF_REL_
	BA_REL_
	BA_DEF_DEF_REL_
	BU_SG_REL_
	BU_EV_REL_
	BU_BO_REL_
	SG_MUL_VAL_

BS_:

BU_: BMS
VAL_TABLE_ normal 1 "bms in normal operation status" 0 "bms not normal operstion status" ;
VAL_TABLE_ vt_sig_limit 1 "require the power limitation" 0 "not req power limitation" ;
VAL_TABLE_ vt_sig_stop 1 "require to stop motor" 0 "NOT require to stop motor" ;
VAL_TABLE_ vt_sig_fault 1 "BMS fault" 0 "BMS does NOT fault" ;
VAL_TABLE_ vt_sig_wall_charge 1 "BMS is in wall charge status" 0 "BMS is not in wall charge status" ;


BO_ 577 Message_1: 8 BMS
 SG_ DC_curr_value : 48|16@1+ (0.1,0) [0|3276.7] "" Vector__XXX
 SG_ Regenerative_curr_limit_value : 32|16@1+ (0.1,0) [0|3276.7] "" Vector__XXX
 SG_ Drive_curr_limit_value : 16|16@1+ (0.1,0) [0|3276.7] "" Vector__XXX
 SG_ SOC : 8|8@1+ (1,0) [0|100] "" Vector__XXX
 SG_ normal : 4|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ limit : 3|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Stop : 2|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Fault : 1|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Wall_charge : 0|1@1+ (1,0) [0|1] "" Vector__XXX



BA_DEF_ BO_  "GenMsgCycleTime" INT 0 10000;
BA_DEF_  "BusType" STRING ;
BA_DEF_DEF_  "GenMsgCycleTime" 0;
BA_DEF_DEF_  "BusType" "";
BA_ "GenMsgCycleTime" BO_ 577 100;
VAL_ 577 normal 1 "bms in normal operation status" 0 "bms not normal operstion status" ;
VAL_ 577 limit 1 "require the power limitation" 0 "not req power limitation" ;
VAL_ 577 Stop 1 "require to stop motor" 0 "NOT require to stop motor" ;
VAL_ 577 Fault 1 "BMS fault" 0 "BMS does NOT fault" ;
VAL_ 577 Wall_charge 1 "BMS is in wall charge status" 0 "BMS is not in wall charge status" ;

