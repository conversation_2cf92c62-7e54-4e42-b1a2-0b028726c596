#include "n32g45x.h"
#include "board_config.h"
#include "board.h"
#include "hal_os.h"
#include "type.h"
#include <stdio.h>

//================================================ NVIC_SystemReset
void mcu_do_reset(void)
{
    NVIC_SystemReset();
}

//================================================ irq_lock
unsigned int irq_lock(void)
{
    unsigned int key;
    key = __get_PRIMASK();
    __disable_irq();
    return key;
}
void irq_unlock(unsigned int key)
{
    __set_PRIMASK(key);
}

 
void NMI_Handler(void)
{
    APP_PRINTF("NMI INT accur!\r\n");
    NVIC_SystemReset();
}
void HardFault_Handler(void)
{
    APP_PRINTF("HardFault INT accur!\r\n");
    NVIC_SystemReset();
}
void BusFault_Handler(void)
{
    APP_PRINTF("BusFault INT accur!\r\n");
    NVIC_SystemReset();
}
void UsageFault_Handler(void)
{
    APP_PRINTF("UsageFault INT accur!\r\n");
    NVIC_SystemReset();
}

void Default_Handler(void)
{
    APP_PRINTF("Unexpected INT accur!\r\n");
    NVIC_SystemReset();
}



