---- agus_malloc Matches (19 in 5 files) ----
hw_agus_event.c (D:\EIKTO projects\2024\ForkLift\BMS_MASTER\src\bsp\driver):    msg_evt_buffer = (u8 *)agus_malloc(msg_length);
hw_agus_event.c (D:\EIKTO projects\2024\ForkLift\BMS_MASTER\src\bsp\driver):    msg_evt_buffer = (u8 *)agus_malloc(msg_length+1);
hw_agus_mem.c (D:\EIKTO projects\2024\ForkLift\BMS_MASTER\src\bsp\driver):void *agus_malloc(uint16_t size)
hw_agus_mem.h (D:\EIKTO projects\2024\ForkLift\BMS_MASTER\src\bsp\driver):void *agus_malloc(uint16_t size);
hw_ec200_communication .c (D:\EIKTO projects\2024\ForkLift\BMS_MASTER\src\bsp\driver):    uint8_t *pSendBuffer = (uint8_t *)agus_malloc(128);
hw_ec200_communication .c (D:\EIKTO projects\2024\ForkLift\BMS_MASTER\src\bsp\driver):    uint8_t *pSendBuffer = (uint8_t *)agus_malloc(64);
hw_ec200_communication .c (D:\EIKTO projects\2024\ForkLift\BMS_MASTER\src\bsp\driver):    uint8_t *pSendBuffer = (uint8_t *)agus_malloc(512);
hw_ec200_communication .c (D:\EIKTO projects\2024\ForkLift\BMS_MASTER\src\bsp\driver):        uint8_t *pMessage = (uint8_t *)agus_malloc(256);
hw_ec200_communication .c (D:\EIKTO projects\2024\ForkLift\BMS_MASTER\src\bsp\driver):    uint8_t *pSendBuffer = (uint8_t *)agus_malloc(1024+128);
hw_ec200_communication .c (D:\EIKTO projects\2024\ForkLift\BMS_MASTER\src\bsp\driver):        uint8_t *pMessage = (uint8_t *)agus_malloc(1024);
hw_ec200_communication .c (D:\EIKTO projects\2024\ForkLift\BMS_MASTER\src\bsp\driver):    uint8_t *pSendBuffer = (uint8_t *)agus_malloc(512);
hw_ec200_communication .c (D:\EIKTO projects\2024\ForkLift\BMS_MASTER\src\bsp\driver):        uint8_t *pMessage = (uint8_t *)agus_malloc(256);
hw_ec200_communication .c (D:\EIKTO projects\2024\ForkLift\BMS_MASTER\src\bsp\driver):    uint8_t *pSendBuffer = (uint8_t *)agus_malloc(512);
hw_ec200_communication .c (D:\EIKTO projects\2024\ForkLift\BMS_MASTER\src\bsp\driver):        uint8_t *pMessage = (uint8_t *)agus_malloc(256);
hw_ec200_communication .c (D:\EIKTO projects\2024\ForkLift\BMS_MASTER\src\bsp\driver):    uint8_t *pSendBuffer = (uint8_t *)agus_malloc(8);
hw_ec200_communication .c (D:\EIKTO projects\2024\ForkLift\BMS_MASTER\src\bsp\driver):                        uint8_t *pSendBuffer = (uint8_t *)agus_malloc(128);
hw_ec200_driver.c (D:\EIKTO projects\2024\ForkLift\BMS_MASTER\src\bsp\driver):        uint8_t *pSendBuffer = (uint8_t *)agus_malloc(1024+128);
hw_ec200_driver.c (D:\EIKTO projects\2024\ForkLift\BMS_MASTER\src\bsp\driver):                            uint8_t *pSendBuffer = (uint8_t *)agus_malloc(128);
hw_ec200_driver.c (D:\EIKTO projects\2024\ForkLift\BMS_MASTER\src\bsp\driver):                    pTableBuffer = agus_malloc(1024);
