充电通讯协议-XG-V3
充电器控制指令集
Revision Date Changes Reviser
0.01 2018/9/15 首次发放
0.02 2018/9/18 增加控制端状态广
播
0.03 2018/9/25 示例代码修正
0.04 2018/10/8 a.增加 3.1 项
0.05 2018/10/15 a.完善充电器应答
内容
0.06 2018/10/26 数据传输间隔改动
0.07 2018/11/8 配置参数指令改动
0.08 2019/3/20 广播请求应答指令
增加内容
0.09 2019/12/18 增加中联环境主动
发送功能
0.10 2020/3/27 增加显示模式
0.11 2020/4/29 修改协议内容
0.12 2020/4/30 描述修订
0.13 2020/7/8 增加广播请求，和
应答内容
0.14 2021/5/14 1.增加充电器应
答广播内容
2.9.显示器发给
充电器指令集定
义说明：9.1.配置
参数
0.15 2022/9/16 1.新增掉电记忆
功能
0.16 2022/9/19 1.重新修订格式
1. 概述
GPD Charger CANbus 通讯是依照并遵循 SAE-J1939 协议。
所有指令集的控制都是通过数据广播(TP.BAM)的形式进行，目的是为了避开
TP.CM(连接管理)中的 TP.RTS(请求连接)&TP.CTS(确认连接)等相关繁琐的过
程。通过广播的形式，加入了 GPD 自主定义的应用。
注意： 
1、TP.BAM + TP.DP 传送完后需要间隔 200ms 才进行下次参数的传输
2、数据格式采用小端格式(LSB + MSB)
3、通讯波特率：250Kbps
2. 支持 ISO 报文指令
指令 PGN
地址声明 60928(0xEE00)
请求地址 59904(0xEA00)
传输协议-连接管理(TP.CM_BAM) 60416(0xEC00)
传输协议-数据传送(TP.DT) 60160(0xEB00)
控制请求 61697(0x00F101)
配置参数 61698(0x00F102)
退出请求 61699(0x00F103)
广播请求 61700(0x00F104)
地址重置请求 61701(0x00F105)
控制端(BMS)状态广播 61702(0x00F106)
掉电记忆清零请求 61704(0x00F108) 
3. J1939 数据域帧格式
优先权 扩展数据页 数据页 PDU 格式 PDU 特定域 源地址 数据域
Field P EDP DP PF PS SA DATA
Bits 3 1 1 8 8 8 64
3.1. 默认源地址分配
Item SA(源地址)
Charger 0x3C
Controler 0x28
4. 充电控制方式
工况简述：
当充电机收到控制请求指令后，会进入被控制模式，控制端通过配置参数指
令，写入需要的目标电流和电压值，充电机将按配置的参数进行输出。
当充电器收到退出请求，充电器则返回自主控制模式。在 BMS 系统中，如果
不是必须，可以不使用此指令，以免充电机的自主功控制功能生效。
一旦控制端发送了控制请求指令，需要每隔 1 秒钟发送 1 次配置参数指令，
用于心跳功能检测，如果充电器在 5 秒钟内没有收到，会超时退出，进入空闲模
式（关闭输出）。
充电器因需要初始化运行数据，设置了触发输出机制。
启动充电器输出需要完成以下步骤：
步骤 1：控制器(0x28)发送控制请求
步骤 2：控制器(0x28)发送配置参数
停止充电器输出：
控制器(0x28)发送配置参数(电压&电流设为 0）
5. 充电器状态数据请求
工况简述：
充电器每收到 1 次广播请求指令，会应答 1 次。充电器不会重复广播状态信
息。广播内容包含了机型信息、故障、控制状态等信息，详情请参见对应的 PGN
列表说明。
控制端需要周期的去读取状态，通过故障和控制状态位来判断充电器的运行情
况。
6. 指令说明
6.1. 控制请求 61697(0x00F101)
Transmission Repetition Rate ms
Default Priority 6
Extended Data Page 0
Data Page 0
PDU Fomat 241
PDU Specific 1
Data Length 2
Parameter Group Number 0x00F101
Byte Start Position Length Description
1-2 2 约定字: 0x01 0xF1
说明：
充电器收到指令后会清除输出数据和电量值，同时解锁“配置参数”指令。
应用示例：(充电器的源地址为 0x3C，控制方的源地址为 0x28)
请求方发送：(ID+Byte 0~7)
TP.BAM： 0x1CECFF28 0x20 02 00 01 FF 01 F1 00 
TP.DP ： 0x1CEBFF28 0x01 01 F1 FF FF FF FF FF
6.1.1. 控制请求应答 127232(0x01F100) 
Transmission Repetition Rate ms
Default Priority 6
Extended Data Page 0
Data Page 0
PDU Fomat 241
PDU Specific 1
Data Length 2
Parameter Group Number 0x01F100
Byte Start Position Length Description
1-2 2 约定字:0xF1 0x01
说明：
这是充电器的应答 PGN,回复格式如示例。
充电机应答：(ID+Byte 0~7)
TP.BAM： 0x1CECFF3C 0x20 02 00 01 FF 00 F1 01
TP.DP ： 0x1CEBFF3C 0x01 F1 01 FF FF FF FF FF
6.2 配置参数 61698(0x00F102) 
Transmission Repetition Rate 1000 ms
Default Priority 6
Extended Data Page 0
Data Page 0
PDU Fomat 241
PDU Specific 2
Data Length 5
Parameter Group Number 0x00F102
Byte Start Position Length Description
1-2 2
目标输出电压值（如：输出 25v,需转
为 25*100=>0x09C4
3-4 2
目标输出电流值（如：输出 30A,需转
为 30*100=>0x0BB8
5 1
LED 指示控制寄存器: 
0x01:关闭显示
0x02:闪烁 50%;(SOC=0~50%)
0x03:闪烁 75% (SOC=51~75%)
0x04:闪烁 100%(SOC=76~99%)
0x05:LED 全亮 (SOC=100%)
说明：
当输出电压值或输出电流值配置为 0 时，充电器会关闭输出，并且此指令会被上
锁；如果需要恢复此指令，需要发送 1 次“控制请求”指令。
应用示例：(充电器的源地址为 0x3C，控制方的源地址为 0x28)
请求方发送：(ID+Byte 0~7)
TP.BAM： 0x1CECFF28 0x20 05 00 01 FF 02 F1 00
TP.DP ： 0x1CEBFF28 0x01 C4 09 B8 0B 05 FF FF
6.2.1 配置参数应答 192768(0x02F100) 
Transmission Repetition Rate 1000 ms
Default Priority 6
Extended Data Page 0
Data Page 0
PDU Fomat 241
PDU Specific 2
Data Length 5
Parameter Group Number 0x02F100
Byte Start Position Length Description
1-2 2
实际电压值.如,0x09E0=D2528, 
2528/100=25.28VDC
3-4 2
实际电流值.如,0x0BB8=D3000,
3000/100=30.00A
5 1
机器状态位:
BIT0: 硬件状态 (0:正常 1:硬件故障) 
BIT1: 温度状态 (0:正常 1:机器高温) 
BIT2: 输入电压状态( 0:正常 1:异常) 
BIT3: 工作状态(0:Standby 或工作中
1:输出关闭 )
说明：
这是充电器的应答 PGN,回复格式如示例。
充电机应答：(ID+Byte 0~7)
TP.BAM： 0x1CECFF3C 0x20 05 00 01 FF 00 F1 02 
TP.DP ： 0x1CEBFF3C 0x01 E0 09 B8 0B 00 FF FF
6.3 退出请求 61699(0x00F103) 
Transmission Repetition Rate ms
Default Priority 6
Extended Data Page 0
Data Page 0
PDU Fomat 241
PDU Specific 3
Data Length 2
Parameter Group Number 0x00F103
Byte Start Position Length Description
1-2 2 约定字:0xF103
应用示例：(充电器的源地址为 0x3C，控制方的源地址为 0x28)
请求方发送：(ID+Byte 0~7)
TP.BAM： 0x1CECFF28 0x20 02 00 01 FF 03 F1 00 
TP.DP ： 0x1CEBFF28 0x01 03 F1 FF FF FF FF FF
6.3.1 退出请求应答 258304(0x03F100) 
Transmission Repetition Rate ms
Default Priority 6
Extended Data Page 0
Data Page 0
PDU Fomat 241
PDU Specific 3
Data Length 2
Parameter Group Number 0x03F100
Byte Start Position Length Description
1-2 2 约定字:0xF103
说明：
这是充电器的应答 PGN,回复格式如示例。
充电机应答：(ID+Byte 0~7)
TP.BAM： 0x1CECFF3C 0x20 02 00 01 FF 00 F1 03 
TP.DP ： 0x1CEBFF3C 0x01 F1 03 FF FF FF FF FF
6.4 广播请求 61700(0x00F104) 
Transmission Repetition Rate ms
Default Priority 6
Extended Data Page 0
Data Page 0
PDU Fomat 241
PDU Specific 4
Data Length 2
Parameter Group Number 0x00F104
Byte Start Position Length Description
1-2 2 约定字:0xF104
应用示例：(充电器的源地址为 0x3C，控制方的源地址为 0x28)
请求方发送：(ID+Byte 0~7)
TP.BAM： 0x1CECFF28 0x20 02 00 01 FF 04 F1 00 
TP.DP ： 0x1CEBFF28 0x01 04 F1 FF FF FF FF FF
 
6.4.1 广播请求应答 323840(0x04F100) 
Transmission Repetition Rate ms
Default Priority 6
Extended Data Page 0
Data Page 0
PDU Fomat 241
PDU Specific 4
Data Length 20
Parameter Group Number 0x04F100
Byte Start Position Length Description
1-2 2
输出端电压值.如,0x09E0=D2528, 
2528/100=25.28VDC
3-4 2
输出电流值.如,0x0BB8=D3000,
3000/100=30.00A
5-6 2
当前电量值.
For example,0x01=D1, 1/10=0.1Ah
7
1
故障代码:
0x00 : 正常
0x01 : 没有检测到电池
0x02 : 市电异常
0x03 : 机器高温保护
0x04 : 电池高温保护
0x05 : 机器内部故障
0x06 : 输出端检测到高电压
0x07 : 地址声明错误
8 1
充电阶段代码：
0x02 : BULK Stage
0x03 : Absorption Stage 0x04 : Finish 
Stage 0x05 : End Charging
未定义值或在 BMS 时,请忽略。
9 1
电池温度值.
For example,0x23=D35, 35-10=25℃
10 1
充电器机内温度值.
For example,0x28=D40, 40-10=30℃
11-12 2
市电电压值.
For example,0x55F0=D22000,
22000/100=220v
13 1 市电频率值。如，0x32=50Hz
14-15 2 机型参数代码.如，0x1E18 = 30A 24v
16
1
控制状态查询:
0x00: 充电器处于自主控制状态
0x01: 充电器处于 BMS 控制状态
0x02: 充电器执行配置参数指令
0x03: 充电器锁定配置参数指令
0x04: 充电器无法启动输出(无 AC 输
入)
0x05: 充电器中断输出(原因请查看
故障代码)
17~18 2
当前曲线号：
For example:0x02 为 b02 曲线号
19 1 当前固件版本号：
For example:0x02 表示版本号为 0.02
20 1 估算电量值：
For example:0x0A 表示电量为 10%
21 1 市电流限流值：
0x00:6A
0x01:10A
0x02:16A
22~23 2 充电器有效充电次数：
For example: 0x02 表示 2 次有效充电
次数(每次吸合继电器，输出时间超过
10min 计一次有效充电)
注意：
主动发送：充电器开始充电后，每隔 5 秒发送一次报文，直至充电结束。
被动发送：当控制端发送广播请求时，充电器会被动应答广播请求。
充电机应答：(ID+Byte 0~7)
TP.BAM： 0x1CECFF3C 0x20 17 00 04 FF 00 F1 04 
TP.DP ： 0x1CEBFF3C 0x01 E0 09 B8 0B 01 00 00
TP.DP ： 0x1CEBFF3C 0x02 00 23 28 F0 55 32 1E
TP.DP ： 0x1CEBFF3C 0x03 18 00 00 02 02 0A 02
TP.DP ： 0x1CEBFF3C 0x04 18 00 FF FF FF FF FF
6.5 地址重置请求 61701(0x00F105)——备用 
6.6 控制端(BMS)状态广播 61702(0x00F106) 
Transmission Repetition Rate 2000 ms
Default Priority 6
Extended Data Page 0
Data Page 0
PDU Fomat 241
PDU Specific 6
Data Length 26
Parameter Group Number 0x00F106
Byte Start Position Length Description
1 1
BMS Status:
0x00 : B-Pack is idle 
0x01 : B-Pack is living in 
Discharge mode
0x02 : B-Pack is living in
Charge mode
2 1
State Of Charging:( Based on 
BMS Status)
example:70%
3~4 2
Remaining Time:( Based on 
BMS Status)
example: 12.3hours(0.1H/bit)
5 1
Heating Temperature: 
example: 25℃
6 1
Battery Temperature: 
example: 25℃
7 1
Cell Temperature#1:
example: 25℃
8 1
Cell Temperature#2:
example: 25℃
9 1
Cell Temperature#3:
example: 25℃
10 1
Battery Pack Temperature:
example: 25℃
11 1
Ambient Temperature:
example: 25℃
12~15 4
Total of charging:
example:123Times
16~19 4
Total of discharging:
example:123Times
20 1
Battery Pack Stauts: 
0x00: No Error 
Bit0:1=High Current with 
Charging
Bit1:1=Low Voltage with 
Discharging
Bit2:1=High Voltage with 
Charging
Bit3:1=Low voltage with cell 
说明：
BMS 需要周期性(2 秒)广播出电池状态。应用示例：
BMS 周期发送：(ID+Byte 0~7)
TP.BAM： 0x1CECFF28 0x20 1A 00 04 FF 06 F1 00
TP.DP ： 0x1CEBFF28 0x01 00 46 7B 00 19 19 19
TP.DP ： 0x1CEBFF28 0x02 19 19 19 19 7B 00 00
TP.DP ： 0x1CEBFF28 0x03 00 7B 00 00 00 00 00
TP.DP ： 0x1CEBFF28 0x04 46 D2 04 D2 04 FF FF

