// Charger_protocol.h

#ifndef _CHAGER_PROTOCOL_H_
#define _CHAGER_PROTOCOL_H_
#include "Charger_protocol.h"
#include "stdint.h"
#include "drv_ring_queue.h"
#include <stdbool.h>
#include <string.h>

/*
CommProtocol.chg			    ��̨����ͨѶЭ��ѡ��
// GB	                      ������ͨѶЭ��
// UDAN					  ����BMS�����250Kbps����BMSֹͣ���ԭ��_V2.3
// YiJiaTongV2.0             004_�������ͨ���Э��(���Ӽ���ģʽ)20190213
// QianGaiLi                 Ǧ��﮲泵���ͨѶЭ��
// BanYiTong_Chg_V1.2        �Ϸʰ���ͨBMS-CAN2.0AͨѶЭ��V1.2_���
// ADY_V1.1                  ADYPower����CANͨѶЭ��V1.1
// ShiNeng                   ʩ��BMSͨѶЭ�� V3.2�汾��������
// CAN2.0C                   ���CAN2.0CЭ��
// TieCheng_Soc              ���Э�飨����SOC��
// LongGong_Chg              007_������ѹ﮵�س��Э��2022.07.29
// XuGong_20190821           1_1_�칤�泵��﮵���������ϣ����Э�飩
// HaoSheng                  ��ʢBMSͨ��Э��
// YiJiaTong_QianTuo         �������ͨ���Э��(���Ӽ���ģʽ)20190213+BMS-CAN2.0A ͨ��Э��V1.02_20190428
// YiJ<PERSON>Tong_QianTuo_ZOWELL  �������ͨ���Э��(���Ӽ���ģʽ)20190213+BMS-CAN2.0A ͨ��Э��V1.02_20190428+ZOWELL��Э��V2.0
//ZOWELL:������ YiJiaTong�����  QianTuo�Ǳ�
*/

// ����Э��ö�٣���ָ���ײ�����Ϊ uint8_t
typedef enum
{
    procesID_GB = 0,
    procesID_UDAN,
    procesID_YiJiaTongV2_0,//2
    procesID_QianGaiLi,
    procesID_BanYiTong_Chg_V1_2,
    procesID_ADY_V1_1,
    procesID_ShiNeng,
    procesID_CAN2_0C,
    procesID_TieCheng_Soc,
    procesID_LongGong_Chg,//9
    procesID_XuGong,
    procesID_HaoSheng,
	procesID_Banyitong_V1_2,
    procesID_LigaoV4_03,
    procesID_Ruyi,
	procesID_LongGong_heater_Chg,//15 ŵ��Э��(������Э��+����)
	procesID_HeLi,//16
	procesID_ALTRACK,//17 ALTRACK充电协议

//procesID_QianTuo,
// procesID_YiJiaTong_QianTuo,
// procesID_YiJiaTong_QianTuo_ZOWELL,
    procesID_MAX_ID
} procesID_t;

// 0=�� ; 1=��� ; 2=����; 3=���+����;4=����;5=��Ļ;
typedef enum
{
    CAN_DISABLE = 0,       // ��ֹ
    CAN_FAST_CHARGE = 1,   // ���
    CAN_CHARGE_VEHICE = 2, // ����
    CAN_FAST_VEHICE = 3,   // ���+����
    CAN_DEBUG = 4,         // ����
    CAN_DisPalay = 5,

} CAN_FUNCTION_t;


typedef struct
{
    uint8_t processID; // Э������

    uint8_t baud; // ������

    void (*processInit)(void); // Э���ʼ��, void (*processInit)(CAN_ID ID, CANBAUD_t baudrate);
    void (*processTask)(void);
    void (*processSend)(DCCanMsg msg); // ���Ĵ���
		void (*recv_func_t)(/*const */ DCCanMsg *msg);
    //  void (*timeout_handler)(void);   //can���ճ�ʱ����
    //  int (*poll)(void);  //��ѯ����   ���������������ݰ���ʱ���     ����oxff��ʾ������    0�������      (����1��ʾ������ԴҪ�л�Ϊ24V)
} Charge_handlerType;

extern Charge_handlerType *ChargeProtocol; 
extern DCCanMsg CAN_DcCh_Recv_Queue;



extern int set_signals_value(unsigned short start_id, void *value, int count);
extern int get_signals_value(unsigned short start_id, void *value, int count);
extern unsigned int get_sys_tick(void);
extern int CAN1_Hard_Init(CANBAUD_t);
extern void task_AssistPowermonitor_OBC(void);
extern void Charger_OFF(void);
extern void Charger_ON(void);
// ʾ������ʵ��

#endif

