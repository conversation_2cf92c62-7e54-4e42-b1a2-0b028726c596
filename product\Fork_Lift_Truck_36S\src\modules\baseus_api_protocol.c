
#include "hal_os.h"
#include "type.h"
#include "board_config.h"
#include "drv_rtc.h"
#include "drv_flash.h"
#include "battery_dev.h"
#include "board.h"
#include "crc.h"
#include "serial_comm.h"
#include "upgrade_common.h"
#include <string.h>
#include <stdlib.h>

/*******************************************************************************************************************************
	baseus_api_protocol.c文件说明:
	
	一.方案：
	1.多设备合成bin文件发送到485总线上各自根据状态机做解析。文件下发后各设备读取到bin文件头，进入各自状态机

	二.Bin文件格式：
	1.文件头魔幻数 64Byte        0x2e 42 41 53 45 55 53 2e  (.BASEUS.) 
	2.固件信息     32Byte        主机：0x00000001   从机：Ox00000002   BMS:0x00000003||BMSV 
 	 
	三.解析策略与状态机简介
	1.起始帧  U S 
		默认只有主机应答
	2.传输帧  U D
	(1).空闲状态 ：识别文件头，本机号则存储当前帧保存偏移地址后转移传输状态，其他机号则转移缄默状态
	(2).缄默状态：遍历每帧的buf，查出文件头magic，本机号则存储当前帧保存偏移地址后转移到传输模式
	(3).传输模式：保存固件后遍历Buf，找出0xffffff，判断文件是否传输完毕，传输完毕则复位更新
		
	3.查询传输完成帧 R C 
	4.查询升级完成帧 U C
	
	四：固件合成规则:
	1.BMS固件必须放到最后，BMS负责供电所以放到最后;
	2.文件头magic地址必须放在一行的开头
	3.文件间隔填充32Byet
********************************************************************************************************************************/

#define TEST_MODEL_ENABLE
//#define BMSV     0x56534d42 //固件识别BMSV
//#define BMSV     0x42 4d 53 2e //固件识别BMS.
#define BMSV     0x2e534d42 //固件识别BMS.
#define DEV_NUMB 0x00000001 //BMS

typedef enum 
{
	ANSWER				= 0x0000,//成功原帧返回 
	SILENT 				= 0x000A,//缄默
	CHECK_ERR 			= 0x1001,
	CMD_ERR 			= 0x1002,
	FLASH_WRITE_ERR		= 0x1201,
	FLASH_READ_ERR 		= 0x1202,
	FARME_SEQUENCE_ERR 	= 0x1301,
	FARME_LENGHT_ERR   	= 0x1302,
	FARME_RECEIVE_ERR  	= 0x1401,
}ENUM_ERR;

#define SET_SIZE 8
BASEUS_OTA_PROTOCOL_t p_serial_comm;
SettingPara_t * bk_ram_fsm = (SettingPara_t *)SETTING_PARA_SAVE_RAM_ADDR;
 
static ENUM_ERR fsm_silent(BASEUS_OTA_PROTOCOL_t* p_serial_comm)
{
	int ret;
	long long temp_magic = 0;
	int dev_numb;
	
	unsigned int addr_write = APP_ADDR_BK_FLASH_BEGIN;	
 
	memcpy(&temp_magic,&p_serial_comm->info_serial_buf[4],8);
	memcpy(&dev_numb,&p_serial_comm->info_serial_buf[4+8],4);
	
	if((PROGRAM_MAGIC_NUMBER == temp_magic) && (dev_numb == BMSV))
	{
		p_serial_comm->first_frame_data_save = &p_serial_comm->info_serial_buf[4];//提取buf内的固件部分
		p_serial_comm->first_frame_data_size = p_serial_comm->info_size - 4;//偏移量
		ret = drv_flash_write(addr_write,p_serial_comm->first_frame_data_save,p_serial_comm->first_frame_data_size);
		if (ret < 0)
			return FLASH_WRITE_ERR;
		
		bk_ram_fsm->fsm_step = OTA_TRANSFER; 

		return ANSWER;
	}
		
	
	return SILENT; 
}

 
#define WRITE_FLASH_SIZE 64  //buf 固件部分64B
static unsigned int ota_download_file_offset = 0;	
 
#define FLAG_END 0xffffffffffffffff
static ENUM_ERR fsm_transfer(BASEUS_OTA_PROTOCOL_t* p_serial_comm)
{
	int dev_numb;	
    int ret;
	unsigned char *pframe_buf = p_serial_comm->info_serial_buf;
//	unsigned int rt_frame_numb = (unsigned short)pframe_buf[0]<<8|pframe_buf[1];
//	unsigned int total_frame_numb = (unsigned short)pframe_buf[2]<<8|pframe_buf[3];
	unsigned char *bin_pframe_buf = &pframe_buf[4];
	long long flag_end =0;
    unsigned int addr_write = ota_download_file_offset + APP_ADDR_BK_FLASH_BEGIN + p_serial_comm->first_frame_data_size;

	memcpy(&flag_end,&p_serial_comm->info_serial_buf[4],SET_SIZE);
 	memcpy(&dev_numb,&p_serial_comm->info_serial_buf[4+8],4);
	if((flag_end == PROGRAM_MAGIC_NUMBER)&&(dev_numb != BMSV))//if buf前8位为.baseus. 继续判断dev,是自己的则继续传，不是自己的则校验
	{
 		bk_ram_fsm->fsm_step = OTA_FREE;
		if (upgrade_check_image() < 0)//下载的数据校验，错误则下载失败
		{
 
			drv_flash_erase(APP_ADDR_BK_FLASH_BEGIN,SIZES_MAX_APP_BK_FLASH);
			return FLASH_WRITE_ERR;
		}
		else
		{
			p_serial_comm->flag_receive_comp=1;
			bk_ram_fsm->fsm_step = OTA_FREE;
			return SILENT;
		}
	}
	else
	{
		if(flag_end == FLAG_END)
		{
			return ANSWER;
		}
		else if(p_serial_comm->flag_receive_comp != 1)//flash_write
		{
			ret = drv_flash_write(addr_write,bin_pframe_buf,WRITE_FLASH_SIZE);
			ota_download_file_offset += ret;		
			if (ret < 0)
				return FLASH_WRITE_ERR;
		}
	}
	return ANSWER;
}	
 	
 
static ENUM_ERR fsm_idle(BASEUS_OTA_PROTOCOL_t* p_serial_comm)
{
	int dev_numb =0;	
	long long magic_numb = 0; 
	
	memcpy(&magic_numb,&p_serial_comm->info_serial_buf[4],8);
	memcpy(&dev_numb,&p_serial_comm->info_serial_buf[4+8],4);

	if( magic_numb == PROGRAM_MAGIC_NUMBER)
	{
		if(dev_numb == BMSV)
		{
			 p_serial_comm->first_frame_data_size = 0;
			bk_ram_fsm->fsm_step = OTA_TRANSFER; //状态转移->传输模式
			int ret = fsm_transfer(p_serial_comm);//写入当前buf的固件部分，记录flash偏移量
			return ANSWER;	
		}
	}
	return SILENT;
}


/*
名称：状态机处理
返回值：ENUM_ERR
*/
static ENUM_ERR ota_fsm(BASEUS_OTA_PROTOCOL_t* ota_prot)
{
	ENUM_ERR ret;
	switch(bk_ram_fsm->fsm_step)
	{
		case OTA_FREE:{
			ret = fsm_idle(ota_prot);
		} break;

		case OTA_SILENT:{
			ret = fsm_silent(ota_prot);
		} break;

		case OTA_TRANSFER:{
			ret = fsm_transfer(ota_prot);
		} break;

		default: bk_ram_fsm->fsm_step = OTA_FREE;
	}
	return ret;
}


static void jump_to_boot(void)
{
	unsigned int *ptr = (unsigned int *)(BOOT_ADDR_FLASH_BEGIN + 4);
	if (*ptr ==  0xFFFFFFFF)//no boot code, stay in app
	{
		return;
	}
	void(*boot_entry)(void) = (void(*)(void))(*ptr);
	irq_lock();
	boot_entry();
	//never be runing below
	reset_board(WARM_RESET_UPGRADE);
}
 
/*
名称：数组接收函数
返回值：应答帧字节长度
*/
static unsigned int tick_over_time = 0;

#define SET_BUF_SIZE 11
#define DEV_ID 0x01

int api_485bus_rx_proc(unsigned char* s_serial_buf,unsigned char buf_size)
{
	ENUM_ERR frame_err = ANSWER;

	p_serial_comm.soi_0x19 = s_serial_buf[0];
	if(p_serial_comm.soi_0x19 != 0x19)
	{
		return 0;
	}
	
	tick_over_time = get_sys_tick();
	
	p_serial_comm.crc16 = (unsigned short)s_serial_buf[buf_size-2]; 
	p_serial_comm.crc16 |= (unsigned short)s_serial_buf[buf_size-3]<<8; 
	unsigned short crc_calc;
    crc_calc = crc16_calc(0xFFFF,s_serial_buf,buf_size-3);	
	if(crc_calc != p_serial_comm.crc16)
	{
		frame_err = CHECK_ERR;
	}

	p_serial_comm.dev_id = s_serial_buf[1];
	p_serial_comm.cmd = (unsigned short)s_serial_buf[2]<<8;
	p_serial_comm.cmd |= s_serial_buf[3];
	p_serial_comm.ret_cmd = 0;
	p_serial_comm.info_size = (unsigned short)s_serial_buf[6]<<8;
	p_serial_comm.info_size |= s_serial_buf[7];
	p_serial_comm.info_serial_buf = &s_serial_buf[8];
	p_serial_comm.end_cmd = 0x20;

	if(p_serial_comm.cmd == FARME_START)//起始帧
	{
		ota_download_file_offset = 0;
		bk_ram_fsm->fsm_step = OTA_FREE;
		bk_ram_fsm->flag_bk_upgrade_comp = DEFAULT_VALUE;
		p_serial_comm.first_frame_data_size = 0;
		drv_flash_erase(APP_ADDR_BK_FLASH_BEGIN,SIZES_MAX_APP_BK_FLASH);		
	}
	else if(p_serial_comm.cmd == FARME_INFO)//传输帧
	{
		frame_err = ota_fsm(&p_serial_comm);
	}
	else if(p_serial_comm.cmd == CHECK_RECEIVE_COMPLETE)
	{
		if(bk_ram_fsm->flag_bk_upgrade_comp == DEFAULT_VALUE)
		{
			frame_err = FARME_SEQUENCE_ERR;//错误	
		}
		else
		{
			p_serial_comm.flag_receive_comp=0;					
		}
		frame_err = SILENT;//silent
	}
	else if(p_serial_comm.cmd == CHECK_UPGRADE_COMPLETE)
	{
		if(bk_ram_fsm->flag_bk_upgrade_comp == DEFAULT_VALUE)
		{
			#ifndef TEST_MODEL_ENABLE
			if(DEV_NUMB != 0x03)
			{
				return 0;//缄默状态
			}
			#endif
			frame_err = FLASH_WRITE_ERR;//完成
			if (upgrade_check_image() < 0)//下载的数据校验，错误则下载失败
			{

				drv_flash_erase(APP_ADDR_BK_FLASH_BEGIN,SIZES_MAX_APP_BK_FLASH);
				return FLASH_WRITE_ERR;
			}
			else//成功
			{
				LoadImageHeader_t *pheader = (LoadImageHeader_t *)APP_ADDR_BK_FLASH_BEGIN;
				if (pheader->target_type == IMAGE_TYPE_APP)
				{
					SettingPara_t * para = (SettingPara_t *)SETTING_PARA_SAVE_RAM_ADDR;
					para->wakeup_signal_type = 1;
					bk_ram_fsm->flag_bk_upgrade_comp = RST_UPGRADE_STEP1;
					//set_signals_value(SID_BOARD_RESET, NULL, 1);
					jump_to_boot();
					return 0;
				}
				return ANSWER;
			}
		}
		else
		{
			bk_ram_fsm->flag_bk_upgrade_comp = DEFAULT_VALUE;
		}
	}
	else
	{
		frame_err = SILENT;
	}

	unsigned char out_buf_size = SET_BUF_SIZE; 
	//s_serial_buf[1] = 1;
	s_serial_buf[4] = frame_err>>8;	//命令标识
	s_serial_buf[5] = frame_err;
	s_serial_buf[6] = 0;	//数据内容长度	
	s_serial_buf[7] = 0;
	crc_calc = crc16_calc(0xFFFF,s_serial_buf,out_buf_size-3);	//crc
	s_serial_buf[8] = (crc_calc>>8) & 0xff;
	s_serial_buf[9] = crc_calc & 0xff;
	s_serial_buf[10] = 0x20;

	//if((frame_err == SILENT)||(ota_flag))
	if(frame_err == SILENT)
	{
		return 0;//缄默状态
	}
	
	return out_buf_size;	
}
 
//接收超时处理
static unsigned char flag_receive_overtime = 0;
static int check_upgrade_comp_task(void)
{
    unsigned int tick;

    tick = get_sys_tick();
	if(tick-tick_over_time <1000)
	{	
		flag_receive_overtime = 0;
		return APP_OK;
	}
	flag_receive_overtime = 1;
 
	return APP_OK;
}
APP_TASK_DEFINE(check_upgrade_comp_task,"50");


//收完毕标志处理
//超时处理
#define TICK_SET 1000
int ota_timeout_task(void)
{
	unsigned int tick_dif = 0;
	static unsigned int s_tick_save = 0;
    unsigned int ticks = get_sys_tick();

	if(flag_receive_overtime == 0)
	{
		s_tick_save = get_sys_tick();
		return 0;
	}
	
	tick_dif = ticks - s_tick_save;
    if (tick_dif < TICK_SET*3) 
	{
		return 0;
	}
	s_tick_save = ticks-(tick_dif-TICK_SET);	
 
	if((flag_receive_overtime)&&(bk_ram_fsm->fsm_step!=OTA_FREE))
	{
		//ota_download_file_offset = 0;	
		bk_ram_fsm->fsm_step = OTA_FREE;	
	}

	if(p_serial_comm.flag_receive_comp)
	{
		p_serial_comm.flag_receive_comp=0;
	}	
	return 0;
}
APP_TASK_DEFINE(ota_timeout_task,"7");



//int ota_timeout_proc(void)
//{
//	p_serial_comm.last_rt_frame=0xffff;
//}



