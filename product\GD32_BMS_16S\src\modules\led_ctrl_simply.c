#include "type.h"
#include "board_config.h"
#include "board.h"
#include "led_ctrl.h"
#include "hal_os.h"
#include "battery_dev.h"
#include "gd32f30x.h"

#ifdef PROGRAM_TYPE_APP
	#define TIME_1000MS  1000*1
	#define TIME_500MS  500
	#define TIME_400MS  400
#endif


char temp_test=0;
static void led_ctrl_task(void)
{
    unsigned int tick;
    static unsigned int tick_save;
	static unsigned short flag_model = 1000;
	
	unsigned char mos_status[2];
	(void)get_signals_value(SID_CMOS_STATUS, mos_status,2);
 
	if(mos_status[0]==0)
	{
		flag_model = 50;
	}
	else if(mos_status[1]==0)
	{	
		flag_model = 300;
	}
	else
	{
		flag_model = 1000;
	}
	
	if(mos_status[0]==0  &&  mos_status[1]==0)
	{
		flag_model = 2000;
	}
	
    tick = get_sys_tick();
	if(tick-tick_save <flag_model)
	{
		return;
	}
	tick_save = get_sys_tick();

	led_runing_flip();
	
}

APP_TASK_DEFINE(led_ctrl_task,"1");


