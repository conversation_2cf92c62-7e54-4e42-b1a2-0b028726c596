/********************************************************************************
*   File Name       : DCCAN.c
********************************************************************************
*   Project/Product :
*   company         :
*   Author          :
********************************************************************************
*直流充电协议
********************************************************************************

********************************************************************************/
#include "GB_T27930_20xx.h"
#include "battery_dev.h"
#include "SCM63X_can.h"
#include "can_SCM63X.h"
#include "drv_ring_queue.h"
#include "Tools.h"
#include "J1939.c"
// #define volt_max 1200
// #define curr_max 400

// extern CAN_FrameQueueType CAN0_Recv_Queue;
extern CAN_FrameQueueType CAN_DCCH_Recv_Queue;
ChargeStateType ChargeState = STATE_IDLE;
BST_PGN6400_InfoTypeDef BST_ERROInfo;
uint16_t CCS_TimeoutCounter = 0;
uint16_t CEM_TimeoutCounter = 0;
uint16_t BRM_TimeoutCounter = 0;
uint16_t BCP_TimeoutCounter = 0;
uint8_t dcch_stBCUChrgRdy;

uint32_t CHM_SPN2600Version; // CHM报文SPN2600 充电机通信协议版本号
uint8_t CRM_stDiscern;		 // 辨识结果（0x00=BMS 不能辨识，0xAA=BMS 能辨识）
uint32_t CRM_no;			 // CRM报文SPN2561  充电机编号
uint32_t CRM_district_no;	 // CRM报文SPN2562  充电机/充电站所在区域编码
uint32_t version_GB;

// u16 65535/10=6553.5A /V
uint16_t CML_OT_volt_max, CML_OT_volt_min;
uint16_t CML_OT_curr_max, CML_OT_curr_min;

// git test

uint16_t chm_output_volt;	  // CCS报文SPN3081 电压输出
uint16_t chm_output_curr;	  // CCS报文SPN3082  电流输出
uint16_t chm_total_chrg_time; // CCS报文SPN3083 充电累计时间

// DCCAN_Recv_Id0x181AF456 variable definition,CST,DC-->BMS
uint8_t chm_stop_factor; // CST报文SPN3521  充电终止原因
uint16_t chm_stop_fault; // CST报文SPN3522  充电终止故障原因
uint8_t chm_stop_err;	 // CST报文SPN3523  充电终止错误原因

// DCCAN_Recv_Id0x181DF456 variable definition,CSD,DC-->BMS
uint16_t chm_acc_chrg_time; // CSD报文SPN3611  累计充电时间
uint16_t chm_output_power;	// CSD报文SPN3612  输出能量 KW.h
uint32_t chm_mac_no;		// CSD报文SPN3613 充电机编号

/*------------MOD定义----------------*/
uint8_t chm_charge_ready = 0xFF;
// uint8_t dcch_stSPN3074ChrgMod;	   // 充电模式（0x01：恒压充电；0x02：恒流充电）
uint16_t BMSChrgUReq = 120; // 电压需求（0.1V）
uint16_t BMSChrgIReq = 1;	// 电流需求（0.1A/位）
boolean_T CHM_bchrgPem;		//

boolean_T HldB_BST; // BMS中止充电
boolean_T HldB_BCL; // 电池需求报文
boolean_T HldB_BRM; // 辆辨识报文
boolean_T HldB_BRO; // 电池充电准备就绪
boolean_T HldB_BCS; // 电池充电总状态

boolean_T HldB_BSM; // 蓄电池状态信息报文
boolean_T HldB_BSD; // BMS统计数据
boolean_T HldB_BEM; // BMS错误报文
boolean_T HldB_BHM; // 车辆握手
boolean_T HldB_BCP; // 充电参数
boolean_T HldB_CTS; // 时间同步信息

/*---------------------OUTPUT to MOD--MOD定义-----------------*/
// 充电机报文标志
// boolean_T HldB_CHM;			  // 充电机握手状态
// boolean_T HldB_CRM;			  // 充电机辨识  两次辨识操作
// boolean_T HldB_CML;			  // 充电机最大输出能力
// boolean_T HldB_CRO_N;			  // 充电机输出准备就绪
boolean_T HldB_CCS; // 充电机充电状态
boolean_T HldB_CEM; // 充电机错误
boolean_T HldB_CST; // 充电机中止充电
// boolean_T	HldB_CSD=0;
uint8_t dcch_stSPN2829BCUChrgRdy; // BMS 是否充电准备好 0x00:未好，0xAA

/*--------------------超时-----------------*/
// 充电机报文标志
boolean_T dcch_bSPN2560OvrTim;	 // SPN3901 接收 SPN2560=0x00 的充电机辨识报文超时
boolean_T dcch_bSPN2560AAOvrTim; // SPN3902 接收 SPN2560=0xAA 的充电机辨识报文超时
boolean_T dcch_bCMLOvrTim;		 // SPN3903 接收充电机的时间同步和充电机最大输出能力报文超时
boolean_T dcch_bSPN2830OvrTim;	 // SPN3904 接收充电机完成充电准备报文超时
boolean_T dcch_bCCSOvrTim;		 // SPN3905 接受充电机充电状态报文超时
boolean_T dcch_bCSTOvrTim;		 // SPN3906  接收充电机中止充电报文超时
boolean_T dcch_bCSDOvrTim;		 // SPN3907  接收充电机充电统计报文超时

boolean_T diw_FCHMRxBMSRcndChrgTiOut;  // 接收电池充电参数报文超时
boolean_T diw_FCHMRxPrmChrgTiOut;	   // 接收电池充电参数报文超时
boolean_T diw_FCHMRxBMSRdyChrgTiOut;   // 接收 BMS 完成充电准备报文超时
boolean_T diw_FCHMRxBattStsChrgTiOut;  // 接收电池充电总状态报文超时
boolean_T diw_FCHMRxBattReqChrgTiOut;  // 接收电池充电要求报文超时（00=正常；01=超时；10=不可信）
boolean_T diw_FCHMRxBMSBreakChrgTiOut; // 接收 BMS 中止充电报文超时（00=正常；01=超时；10=不可信）
boolean_T diw_FCHMRxBMSChrgTiOut;	   // 接收 BMS 充电统计报文超时
/*--------------------停机记录-----------------*/
boolean_T ChStopOV;
boolean_T ChStopOC;
boolean_T ChStopOT;

// boolean_T HldB_V12;
boolean_T HldB_CC2 = CC2_STATE_BREAK;
// 电池电压信息数组，根据枚举类型索引，电压值已转换为mV
const BatteryVoltageInfo BatteryVoltages[] = {
	{2100, 1800, 2400}, // BatTapey_PbA 铅酸
	{1200, 1000, 1500}, // BatTapey_NiMH 镍氢
	{3200, 2500, 3650}, // BatTapey_LiFePO4 磷酸铁锂
	{3700, 2400, 4200}, // BatTapey_LiMn 锰酸锂
	{3700, 2750, 4200}, // BatTapey_LiCoO2 钴酸锂
	{3700, 2750, 4200}, // BatTapey_NCM 三元材料
	{3700, 2750, 4200}, // BatTapey_LiPo 聚合物锂离子
	{2500, 1800, 2500}, // BatTapey_LiTi 钛酸锂
	{0, 0, 0}			// BatTapey_OTHER 其他
};

// SID_DCC_CHG_ONLINE	快充12V接入
// DCC   国标快充
int AssistPowermonitorIoInit_DCC(void)
{
	GPIO_InitPad(AssistPowerMonitorIO_DCC);
	GPIO_ConfigDir(AssistPowerMonitorIO_DCC, GPIO_INPUT);
	return 0;
}
APP_INIT_DEFINE(AssistPowermonitorIoInit_DCC, "10");

boolean_T GetAssistPowermonitor_Statu_DCC(void)
{
	return (boolean_T)GPIO_RdDataIn(AssistPowerMonitorIO_DCC);
}

void task_CC2_StateMonitor(void)
{
	static uint8_t count_10ms = 0;			 // 10ms计数器
	static bool currentState_CC2 = false;	 // 当前CC2状态
	static bool lastState_CC2 = false;		 // 上一次CC2状态
	static uint8_t stateCounter_CC2 = 0;	 // 状态改变后的计数器
	static bool changeStateFlag_CC2 = false; // 状态改变标志

	count_10ms++;
	if (count_10ms < 3)
		return; // 每30ms检测一次
	count_10ms = 0;

	float voltage = adc_CC2_Voltage(); // 读取CC2电压
									   // 转为mV写入字典
	uint16_t V_CC2_mV = ((uint16_t)(voltage * 1000)) / 1000;
	(void)set_signals_value(SIG_FCHG_CC2_VOLTAGE, &V_CC2_mV, 1); // 更新信号值

	bool tempState_CC2 = (voltage < 0.6 * ADC_VREF); // 判断CC2状态，<1.6V表示握手成功

	if (tempState_CC2 == lastState_CC2) // 状态未改变
	{
		if (!changeStateFlag_CC2)
		{
			return; // 如果没有状态改变标志，直接返回
		}
		stateCounter_CC2++;
		if (stateCounter_CC2 >= 3) // 连续3个周期状态保持不变
		{
			currentState_CC2 = tempState_CC2;
			(void)set_signals_value(SIG_CC2_STATE, &currentState_CC2, 1); // 更新信号值

			// 把国标CC2握手也放到这里
			if (voltage <= 0.6 * ADC_VREF)
			{
				HldB_CC2 = CC2_STATE_CONNECT;
			}
			else
			{
				HldB_CC2 = CC2_STATE_BREAK; // 充电连接器故障 CC2
			}
			changeStateFlag_CC2 = false; // 重置改变标志
		}
	}
	else // 状态改变
	{
		changeStateFlag_CC2 = true;	   // 设置状态改变标志
		stateCounter_CC2 = 0;		   // 重置计数器
		lastState_CC2 = tempState_CC2; // 更新上一次状态
	}
}

// 监测DCC 快充辅助电源
void task_AssistPowermonitor_DCC(void)
{
	// 50 ms获取一次状态，当状态变化时，则需要该状态持续三个周期再更新输出状态
	uint16_t xMS_TIME = 50;

	uint32_t timeNow_Xms = 0;
	uint16_t temp = 0;
	static uint32_t time_pre_Xms = 0;
	static boolean_T tempState_DCC = false;
	static boolean_T currentState_DCC = false;
	static boolean_T lastState_DCC = false;
	static uint8_t stateCounter_DCC = 0;

	timeNow_Xms = get_sys_tick();

	if (timeNow_Xms - time_pre_Xms < xMS_TIME)
	{
		return;
	}
	time_pre_Xms = timeNow_Xms;

	tempState_DCC = GetAssistPowermonitor_Statu_DCC();

	if (tempState_DCC != currentState_DCC)
	{
		stateCounter_DCC = 0;
		currentState_DCC = tempState_DCC;
	}
	else
	{
		if (stateCounter_DCC < 2)
		{
			stateCounter_DCC++;
		}
		else if (currentState_DCC != lastState_DCC)
		{
			lastState_DCC = currentState_DCC;
			temp = (lastState_DCC ? 0 : 12000);
			// 处理DCC 12V电源监控逻辑
			(void)set_signals_value(SIG_FCHG_AUXILIARY_VOLTAGE, &temp, 1);
			
		 //temp = 1;
		 set_signals_value(SID_DCC_CHG_ONLINE, &temp, 1); // 更新信号值]
		}
	}
}

// 国标充电机状态监测
// CC2、DCC、BCD、SID_VOLTAGE_BATTERY
// get_signals_value(, &temp, 1);//电池电压
void Task_Charge_Status(void)
{
	uint8_t temp = 0;
	
	if (HldB_CC2 == CC2_STATE_CONNECT )
	{

		temp = 1;
		set_signals_value(SID_DCC_CHG_ONLINE, &temp, 1); // 更新信号值]
	}
	else
	{
		temp = 0;
		set_signals_value(SID_DCC_CHG_ONLINE, &temp, 1); // 更新信号值]
	}	
	
	
	
}

/*---------------------------握手阶段--------------------------*/
// SEND 250ms
// BHM 车辆握手 9984 002700H 6 2 250 BMS-》充电机

void BHM_ID0x182756F4_DATA_CAL(void)
{
	DCCanMsg BHM_Id0x182756f4;
	uint32_t temp = 0;
	// 国标没定义绝缘监测允许电压，暂时定义为当前电池包电压
	get_signals_value(SID_VOLTAGE_BATTERY, &temp, 1); // 电池电压
	// mv->0.1V
	temp = temp / 100;

	BHM_Id0x182756f4.FrameID = 0x182756f4;
	BHM_Id0x182756f4.DataLength = 2;
	BHM_Id0x182756f4.FrameFormat = CAN_FRAME_FORMAT_EFF;
	BHM_Id0x182756f4.FrameType = CAN_FRAME_TYPE_DATA;
	BHM_Id0x182756f4.FrameData[0] = temp;

	BHM_Id0x182756f4.FrameData[1] = temp >> 8;
	DCCAN_SendMsg(DCCH_CAN, &BHM_Id0x182756f4);
}

// SEND 250ms  使用TCPM
// BRM, PGN_512, BMS辨识报文
void BRM_ID0x1C0256F4_DATA_CAL(void)
{
	// j1939_int8_t data[50];
	uint8_t len = 0;
	// uint8_t i = 0;

	// uint32_t temp=0,temp1=0;
	uint8_t data[5];
	BRM_PGN512TypeDef BRM_PGN512;
	len = sizeof(BRM_PGN512TypeDef);
	memset(&BRM_PGN512, 0xFF, len);

	// SID_BATT_RATED_CAPACITY	电池额定容量	uint16
	get_signals_value(SID_BAT_RATED_CAPACITY_THR, &BRM_PGN512.RatedCapacity, 1); // 2字节，整车动力蓄电池系统额定容量/Ah
	BRM_PGN512.RatedCapacity = BRM_PGN512.RatedCapacity * 10;					 // A=>0.1Ah/位

	get_signals_value(SID_CELLS_AMOUNT, data, 5);

	switch (data[4])
	{
	case 0:
		BRM_PGN512.BatteryType = BatTapey_NCM;
		break;
	case 1:
		BRM_PGN512.BatteryType = BatTapey_LiFePO4;
		break;
	case 2:
		BRM_PGN512.BatteryType = BatTapey_LiCoO2;
		break;
	case 3:
		BRM_PGN512.BatteryType = BatTapey_LiMn;
		break;
	case 4:
		BRM_PGN512.BatteryType = BatTapey_LiTi;
		break;

	default:
		BRM_PGN512.BatteryType = BatTapey_OTHER;
		break;
	}

	switch (data[0])
	{
	case 1:
		BRM_PGN512.RatedVoltage = (data[1]) * (BatteryVoltages[BRM_PGN512.BatteryType - 1].RatedVoltage_mV / 100);
		break;
	case 2:
		BRM_PGN512.RatedVoltage = (data[1] + data[2]) * (BatteryVoltages[BRM_PGN512.BatteryType].RatedVoltage_mV / 100);
		break;
	default:
		BRM_PGN512.RatedVoltage = 0;
		break;
	}
	// get_signals_value(SID_BATT_TYPE, &BRM_PGN512.BatteryChargeCycleCount, 1); //0x4000	SID_BATT_CIRCLE_TIMES	电池累计充电次数	uint32
	*(uint32_t *)&BRM_PGN512.BatteryPackSerialNumber = 0x01234578,
			   J1939_TP_TX_Message(BRM, DCFC_ADDR, (j1939_int8_t *)&BRM_PGN512, len);

	HldB_CEM = 0;
}

// Receive 250ms
// CHM, PGN_9728, 充电机握手报文
void CHM_ID0x1826F456_DATA_CAL(DCCanMsg msg)
{

	ChargeState = STATE_HANDSHAKE;

	CHM_SPN2600Version = msg.FrameData[0] + ((msg.FrameData[1] << 8) & 0xffffff00) + ((msg.FrameData[2] << 16) & 0xffff0000);
	version_GB = msg.FrameData[0] | (msg.FrameData[1] << 8) | (msg.FrameData[2] << 16);
	if (DC_CHARGE_PROTOCOL_VERSION2011 == version_GB)
	{
		APP_PRINTF("Protocol:VERSION2011\r\n");
	}
	else if (DC_CHARGE_PROTOCOL_VERSION2015 == version_GB)
	{
		APP_PRINTF("Protocol:VERSION2015\r\n");
	}
	else if (DC_CHARGE_PROTOCOL_VERSION2023 == version_GB)
	{
		APP_PRINTF("Protocol:VERSION2023\r\n");
	}
	else
	{
		// BmsChgerInfo.Protocol = PROTOCOL_NONE;
		return;
	}

	HldB_BHM = 1; // 发送BMH
}

// receive
// CRM 充电机辨识 256 000100H 6 8 250 充电机->BMS
void CRM_ID0x1801F456_DATA_CAL(DCCanMsg msg)
{
	CRM_stDiscern = msg.FrameData[0]; // 辨识结果（0x00=BMS 不能辨识，0xAA=BMS 能辨识）
	// 充电机编号，1/位；0 偏移量；数据范围：0~0xFFFFFFFF
	CRM_no = msg.FrameData[1] + ((msg.FrameData[2] << 8) & 0xffffff00) + ((msg.FrameData[3] << 16) & 0xffff0000) + ((msg.FrameData[4] << 24) & 0xff000000);
	// 充电机/充电站所在的区域编码，标准 ASCII 码
	CRM_district_no = msg.FrameData[5] + ((msg.FrameData[6] << 8) & 0xffffff00) + ((msg.FrameData[7] << 16) & 0xffff0000);
	if (CRM_stDiscern == 0x00)
	{
		HldB_BRM = 1;
		BRM_TimeoutCounter = 500;
	}
	else if (CRM_stDiscern == 0xAA) //
	{
		HldB_BRM = 0;
		HldB_BCP = 1; //
		ChargeState = STATE_COMMUNICATION_SETUP;
	}
	/*****报文终止条件*****/
	HldB_BHM = 0;
	HldB_CST = 0;
}

/*---------------------------参数配置阶段-----------------------------*/

bool PGN1536_SNP_Get(PGN1536_SNP_TypeDef *PGN1536_SNP)
{
	if (!PGN1536_SNP)
		return FALSE;
	int8_t C_RATE;
	uint16_t V_CELL_MAX, Ah_CALL_MAX, NumberCEll, T_MAX, SOC;
	uint32_t V_PACk_NOW;

	// 起始字节1-2 SPN 2816, 单体动力蓄电池最高允许充电电压 数据分辨率：0.01 V/ 位
	// get_signals_value(SID_CELL_OV_Level_2_RAISE_THR, &V_CELL_MAX, 1); // 也可以读电池类型进行计算
	// SID_BAT_TYPE  /*0=三元锂电3.7V   ; 1=磷酸铁锂3.2V     ; 2=钴酸锂; 3=锰酸锂  4=钛酸锂*/

	get_signals_value(SID_CELL_RATED_V_THR, &V_CELL_MAX, 1);	  // mV也可以读电池类型进行计算
	PGN1536_SNP->singleBatteryMaxChargeVoltage = V_CELL_MAX / 10; // mv=>10mv

	// 起始字节3-4 SPN2817 最高允许充电电流数据分辨率：0.1 A/ 位， -400 A偏移量；
	get_signals_value(SID_BAT_RATED_CAPACITY_THR, &Ah_CALL_MAX, 1); // 额定容量· AH
	get_signals_value(SID_CHARGER_C_RATE, &C_RATE, 1);				// SID_CHARGER_C_RATE	充电电流倍率(C)

	if (C_RATE <= 0)
	{
		C_RATE = 1;
	}
	PGN1536_SNP->maxAllowableChargeCurrent = (PGN1536_SNP->maxAllowableChargeCurrent * C_RATE - 400) * 10; // 额定容量AH*充电倍率*10（分辨率0.1A）

	// 起始字节5-6
	// SPN2818 动力蓄电池标称总能量数据分辨率：0.1 kW·h/ 位，0 kW·h 偏移量；数据范围：0～1000 kW·h;
	get_signals_value(SID_CELLS_AMOUNT, &NumberCEll, 1); // SID_CELLS_AMOUNT	电芯数量	uint16
	if (NumberCEll == 0)
		NumberCEll = 16;
	PGN1536_SNP->batteryNominalTotalEnergy = V_CELL_MAX * Ah_CALL_MAX * NumberCEll / 100 / 1000; // V*A*NumberCEll= Wh，Wh/1000=KW。h

	// 起始字节7-8   SPN 2819, 最高允许充电总电压  数据分辨率：0.1 V/ 位，0 V偏移量；
	PGN1536_SNP->maxAllowableTotalChargeVoltage = V_CELL_MAX / 100 * NumberCEll;

	// 起始字节9  SPN 2820, 最高允许温度
	get_signals_value(SID_CELL_TEMPER_MAX, &T_MAX, 1);
	T_MAX = 65 - 50;
	PGN1536_SNP->maxAllowableTemperature = T_MAX;
	// 起始字节10-11  SPN 2821, 整车动力蓄电池荷电状态  数据分辨率：0.1%/位，0%偏移量；数据范围：0～100%;
	get_signals_value(SID_SOC, &SOC, 1);
	SOC = 50;
	PGN1536_SNP->vehicleBatteryStateOfCharge = SOC;

	// 起始字节12-13  SPN2822 整车动力蓄电池当前电池电压 数据分辨率：0.1 V/位，0 V偏移量。 SID_VOLTAGE_BATTERY	电池组电压	uint32	mV
	get_signals_value(SID_VOLTAGE_BATTERY, &V_PACk_NOW, 1);
	// V_PACk_NOW = 50000;
	PGN1536_SNP->vehicleBatteryCurrentVoltage = V_PACk_NOW * 100; // mV=>0.1V

	return TRUE;
}

// SEND 500ms  使用TCPM
// BCP, PGN_1536, 车辆充电参数报文
void BCP_ID0x1C0656F4_DATA_CAL(void) // 充电参数配置报文
{
	uint16_t data_len;
	PGN1536_SNP_TypeDef PGN1536_SNP;

	data_len = sizeof(PGN1536_SNP_TypeDef);

	memset((void *)&PGN1536_SNP, 0, data_len);
	PGN1536_SNP_Get(&PGN1536_SNP);

	J1939_TP_TX_Message(0x000600, DCFC_ADDR, (j1939_int8_t *)&PGN1536_SNP, data_len);
}
// SEND 500ms
// BRO, PGN_2304, 车辆充电准备就绪状态报文
void BRO_ID0x100956F4_DATA_CAL(void) // BMS 充电准备就绪
{
	DCCanMsg BRO_Id0x100956f4;

	BRO_Id0x100956f4.FrameID = 0x100956f4;
	BRO_Id0x100956f4.DataLength = 1;
	BRO_Id0x100956f4.FrameFormat = CAN_FRAME_FORMAT_EFF;
	BRO_Id0x100956f4.FrameType = CAN_FRAME_TYPE_DATA;
	// SPN2829 BMS 是否充电准备好 0x00:未好，0xAA:
	BRO_Id0x100956f4.FrameData[0] = 0xAA; // dcch_stBCUChrgRdy;
	HldB_BRO = 1;
	DCCAN_SendMsg(DCCH_CAN, &BRO_Id0x100956f4); // 等待CRO
}

/*****************************************/
// Receive 500ms
// CTS, PGN_1792, 充电机发送时间同步信息报文
void CTS_ID0x1807F456_DATA_CAL(DCCanMsg msg) //  待定,时间同步信息报文
{
	//	HldB_BCP = 0;
	//	HldB_CTS = 1;
	// 充电机发送给 BMS 的时间同步信息。
}

// Receive 250ms
// CML, PGN_2048, 充电机最大输出能力报文
void CML_ID0x1808F456_DATA_CAL(DCCanMsg msg) // 最大输出能力报文
{
	CML_PGN2048_InfoTypeDef *CML_PGN2048 = (CML_PGN2048_InfoTypeDef *)msg.FrameData;
	if (!CML_PGN2048)
	{
		return;
	}
	// 0.1V/bit 0.1A/bit
	CML_OT_volt_max = CML_PGN2048->highestOutputVoltage; // 最高输出电压
	CML_OT_volt_min = CML_PGN2048->lowestOutputVoltage;	 // 最低输出电压
	CML_OT_curr_max = CML_PGN2048->maxOutputCurrent;	 // 最大输出电流
	CML_OT_curr_min = CML_PGN2048->minOutputCurrent;	 // 最小输出电流
	APP_PRINTF("CML_OT_volt_max:%d,CML_OT_volt_min:%d,CML_OT_curr_max:%d,CML_OT_curr_min:%d\n", CML_OT_volt_max, CML_OT_volt_min, CML_OT_curr_max, CML_OT_curr_min);
	HldB_BCP = 0;
	HldB_BRO = 1;
}

// Receive 250ms
// CRO, PGN_2560, 充电机输出准备就绪状态报文
// 直到在5s内收到 SPN2560=0xAA
void CRO_ID0x100AF456_DATA_CAL(DCCanMsg msg) //
{
	// HldB_CML = 0;   // 充电机最大输出能力

	chm_charge_ready = msg.FrameData[0]; // 充电机是否充电准备好
	if (chm_charge_ready == 0xAA)
	{
		HldB_BRO = 0; //

		HldB_BCL = 1; // 电池需求报文
		HldB_BCS = 1; //
		ChargeState = STATE_CHARGING;
	}
	else
	{
	}
}

/*----------------------------充电阶段-------------------------------*/
// SEND 50ms  先恒流再恒压
// BCL, PGN_4096, 电池充电需求报文
void BCL_ID0x181056F4_DATA_CAL(void)
{
	DCCanMsg BCL_Id0x181056f4;
	BCL_PGN4096_InfoTypeDef *BCL_PGN4096 = (BCL_PGN4096_InfoTypeDef *)BCL_Id0x181056f4.FrameData;
	// ChargeState=STATE_CHARGING;
	memset(&BCL_Id0x181056f4, 0, 8);
	BCL_Id0x181056f4.FrameID = 0x181056f4;
	BCL_Id0x181056f4.DataLength = 5;
	BCL_Id0x181056f4.FrameFormat = CAN_FRAME_FORMAT_EFF;
	BCL_Id0x181056f4.FrameType = CAN_FRAME_TYPE_DATA;

	uint32_t ChargingVoltageMax=0; // mV
	uint32_t ChargingCurrentMax=0; // mA
	get_signals_value(SIG_CHARGE_REQ_VOLTAGE, &ChargingVoltageMax, 1);
	get_signals_value(SIG_CHARGE_REQ_CURRENT, &ChargingCurrentMax, 1);
	// mV、mA转换为0.1V/bit 、0.1A/bit
	//ChargingVoltageMax /= 100;
	//ChargingCurrentMax /= 100;

	// 限制请求电压电流在充电机输出能力范围内
	// CML_OT_volt_max, CML_OT_volt_min;CML_OT_curr_max, CML_OT_curr_min;

	ChargingVoltageMax = ChargingVoltageMax > CML_OT_volt_max ? CML_OT_volt_max : ChargingVoltageMax;
	ChargingVoltageMax = ChargingVoltageMax < CML_OT_volt_min ? CML_OT_volt_min : ChargingVoltageMax;
	ChargingCurrentMax = ChargingCurrentMax > CML_OT_curr_max ? CML_OT_curr_max : ChargingCurrentMax;
	ChargingCurrentMax = ChargingCurrentMax < CML_OT_curr_min ? CML_OT_curr_min : ChargingCurrentMax;
	// 输出电流为400A，BMS输出电流为0~400A,限制在此范围内
	ChargingCurrentMax = ChargingCurrentMax > 4000 ? 4000 : ChargingCurrentMax;

	// 电流偏移400A，举例子：1A=》（400-1）=399；10A=（400-10）=390；还需要换算为0.1A/bit
	ChargingCurrentMax = 4000 - ChargingCurrentMax;

	BMSChrgUReq = BCL_PGN4096->voltageDemand = ChargingVoltageMax;
	BMSChrgIReq = BCL_PGN4096->currentDemand = ChargingCurrentMax;

	get_signals_value(SIG_CHARGE_MODE, &BCL_PGN4096->chargeMode, 1); // 控制位 1CV,2CC

	memcpy(BCL_Id0x181056f4.FrameData, BCL_PGN4096, 6);
	DCCAN_SendMsg(DCCH_CAN, &BCL_Id0x181056f4); // 等待CRO
												// APP_PRINTF("BCL UReq:%d,IReq:%d\n",BCL_PGN4096->voltageDemand ,BCL_PGN4096->currentDemand);
}
// 功能函数 ：计算剩余充电时间
// 输入参数 ：无
// 输出参数 ：无
//(目标SOC - 当前SOC) * 电池容量*1.2 / 充电电流/60
void BMSChrgTimeRemain(void)
{
	uint16_t targetSOC, currentSOC;
	uint32_t batteryCapacity, chargingCurrent;
	get_signals_value(SID_SOC, &targetSOC, 1);													   // 获取目标SOC
	get_signals_value(SID_CURRENT_CAPACITY, &batteryCapacity, 1);								   // 获取电池容量
	get_signals_value(SID_CURRENT_BATTERY, &chargingCurrent, 1);								   // 获取充电电流
	currentSOC = 1000 - targetSOC;																   // 计算当前SOC
	uint32_t remainTime = (currentSOC - targetSOC) * batteryCapacity * 1.2 / chargingCurrent * 60; // 计算剩余充电时间/分钟
	set_signals_value(SID_CHG_OR_DIS_TIME_REMAIN, &remainTime, 1);								   // 设置剩余充电时间
																								   // APP_PRINTF("剩余充电时间：%d\n", remainTime); // 打印剩余充电时间
}

// TCPM 传输
// BCS 电池充电总状态 4352 001100H 7 9 250ms BMS-》充电机
void BCS_ID0x1C1156F4_DATA_CAL(void)
{
	// j1939_int8_t data[10];
	BCS_PGN4352_InfoTypeDef BCS_PGN4352;
	BCS_PGN4352.chargingVoltage = 1;
	uint16_t temp;

	int bat_v = 0;
	get_signals_value(SID_VOLTAGE_BATTERY, &bat_v, 1); // SID_VOLTAGE_BATTERY	电池组电压  SPN3075
	BCS_PGN4352.chargingVoltage = bat_v / 1000;

	int curr = 0;
	get_signals_value(SID_CURRENT_BATTERY, &curr, 1); // SID_CURRENT_BATTERY	电池组电流 SPN3076
	BCS_PGN4352.chargingCurrent = curr / 1000;

	get_signals_value(SID_CELL_VOLTAGE_MAX_SN, &temp, 1); //  SPN3077
	BCS_PGN4352.highestBatteryCellVoltage.bits.groupNumber = temp;
	get_signals_value(SID_CELL_VOLTAGE_MAX, &temp, 1); //    SPN3077
	BCS_PGN4352.highestBatteryCellVoltage.bits.voltage = temp / 10;

	unsigned short soc;
	get_signals_value(SID_SOC, &soc, 1); ////SPN3078   当前荷电状态 SOC
	BCS_PGN4352.soc = soc / 10;

	// SPN3079   估算剩余充电时间
	BMSChrgTimeRemain();
	get_signals_value(SID_CHG_OR_DIS_TIME_REMAIN, &BCS_PGN4352.estimatedChargeTime, 1); //
	// SPN3076 充电电流测量值  数据分辨率：0.1 A/ 位， -400 A偏移量；
	BCS_PGN4352.chargingCurrent = BCS_PGN4352.chargingCurrent + 4000; // A to 0.1A

	J1939_TP_TX_Message(BCS, DCFC_ADDR, (j1939_int8_t *)&BCS_PGN4352, 9);
	APP_PRINTF("BCS U:%d,I:%d\n", BCS_PGN4352.chargingVoltage, BCS_PGN4352.chargingCurrent);
}

// BSM, PGN_4864, 车辆状态信息报文
void BSM_ID0x181356F4_DATA_CAL(void)
{
	//		uint8_t temp = 0;
	uint16_t /*Temp,*/ V_CellMaxSn /*,V_MinCall,V_MinCallSn*/;
	uint16_t T_MaxCall, T_MaxCallSn, T_CellMin, T_CellMinSn;
	DCCanMsg BSM_Id0x181356f4;
	BSM_PGN4864_InfoTypeDef *BSM_PGN4864;
	BSM_PGN4864 = (BSM_PGN4864_InfoTypeDef *)BSM_Id0x181356f4.FrameData;

	BSM_Id0x181356f4.FrameID = 0x181356f4;
	BSM_Id0x181356f4.DataLength = sizeof(BSM_PGN4864_InfoTypeDef);
	BSM_Id0x181356f4.FrameFormat = CAN_FRAME_FORMAT_EFF;
	BSM_Id0x181356f4.FrameType = CAN_FRAME_TYPE_DATA;

	get_signals_value(SID_CELL_VOLTAGE_MAX_SN, &V_CellMaxSn, 1); //
	get_signals_value(SID_CELL_TEMPER_MAX, &T_MaxCall, 1);		 //   偏移量 -50℃最高动力蓄电池温度
	get_signals_value(SID_CELL_TEMPER_MAX_SN, &T_MaxCallSn, 1);	 //   最高温度监测点编号
	get_signals_value(SID_CELL_TEMPER_MIN, &T_CellMin, 1);		 //   最低动力蓄电池温度
	// get_signals_value(SID_CELL_TEMPER_MIN_SN, &T_CellMinSn, 1);	 //   最低温度监测点编号
	T_CellMinSn = (T_MaxCall + T_CellMin) / 2;

	BSM_PGN4864->V_CellMaxSn = V_CellMaxSn;	 // SPN3085最高单体动力蓄电池电压所在编号
	BSM_PGN4864->T_CellMin = T_CellMin + 50; // SPN3086  偏移量 -50℃最高动力蓄电池温度
	BSM_PGN4864->T_CellMaxSn = T_MaxCallSn;	 // SPN3087  最高温度监测点编号
	BSM_PGN4864->T_CellMin = T_CellMin + 50; // SPN3088  最低动力蓄电池温度
	BSM_PGN4864->T_CellMinSn = T_CellMinSn;	 // SPN3089  最低温度监测点编号

	uint8_t Temp[4] = {0};
	uint32_t *pdata = (uint32_t *)Temp;
	// 绝缘监测  SID_LEAKAGE_FAIL_Level_1
	(void)get_signals_value(SID_LEAKAGE_FAIL_Level_1, Temp, 4);
	if (pdata != NULL)
	{
		BSM_PGN4864->status.insulationStatus = 1; // 绝缘状态（00=正常；01=不正常；10=不可信状态）
	}

	// SID_CHG_CELL_OV_Level_1	充电单体过压一级
	// SID_CHG_BAT_UV_Level_1	充电总体欠压一级
	*pdata = 0;
	(void)get_signals_value(SID_CHG_CELL_OV_Level_1, Temp, 4);
	if (pdata != NULL)
	{
		BSM_PGN4864->status.voltageStatus = 1; // 单体电压情况（00=正常；01=过高；10=过低）
	}
	*pdata = 0;
	(void)get_signals_value(SID_CHG_CELL_OV_Level_1, Temp, 4);
	if (pdata != NULL)
	{
		BSM_PGN4864->status.voltageStatus = 2; // 单体电压情况（00=正常；01=过高；10=过低）
	}

	// SID_SOC_LOW_Level_1	SOC低一级

	// SID_SOC	电池组SOC

	*pdata = 0;
	(void)get_signals_value(SID_SOC_LOW_Level_1, Temp, 4);
	if (pdata != NULL)
	{
		BSM_PGN4864->status.socStatus = 2; // 整车 SOC 情况（00=正常；01=过高；10=过低）
	}
	uint16_t SOC_T = 0;
	(void)get_signals_value(SID_SOC, &SOC_T, 1);
	if (SOC_T > 1000)
	{
		BSM_PGN4864->status.socStatus = 1; // 单体电压情况（00=正常；01=过高；10=过低）
	}

	// SID_FCHG_OC_Level_1	快充电过流一级
	*pdata = 0;
	(void)get_signals_value(SID_FCHG_OC_Level_1, Temp, 4);
	if (pdata != NULL)
	{
		BSM_PGN4864->status.chargeCurrentStatus = 1; // 充电电流情况（00=正常；01=过流；10=不可信状态）
	}

	// SID_CHG_OT_Level_1	充电高温一级
	*pdata = 0;
	(void)get_signals_value(SID_CHG_OT_Level_1, Temp, 4);
	if (pdata != NULL)
	{
		BSM_PGN4864->status.tempStatus = 1; // 蓄电池温度情况（00=正常；01=过高；10=不可信状态）
	}

	//
	*pdata = 0;
	(void)get_signals_value(SID_CHG_OT_Level_1, Temp, 4);
	if (pdata != NULL)
	{
		BSM_PGN4864->status.connectorStatus = 0; // 输出连接器状态（00=正常；01=不正常；10=不可信状态）
	}

	// SID_CHG_OR_DIS_FLAG	充放电标志位
	// SID_DRELAY_CTRL	充电控制

	*pdata = 0;
	if (pdata != NULL)
	{
		BSM_PGN4864->status.chargePermission = 1; // 充电允许（00=禁止；01=允许）
	}

	// memcpy(BSM_Id0x181356f4.FrameData, &BSM_PGN4864,BSM_Id0x181356f4.DataLength );
	DCCAN_SendMsg(DCCH_CAN, &BSM_Id0x181356f4);
}

// TCPM 传输 力蓄电池电压报文
// BMV, PGN_5376, 单体蓄电池电压报文
void BMV_ID0x1C1556F4_DATA_CAL(void)
{
}
// TCPM 传输 蓄电池温度报文
// BMT, PGN_5632, 动力蓄电池温度报文
void BMT_ID0x1C1656F4_DATA_CAL(void)
{
}
// TCPM 传输 蓄电池预留报文
// BSP, PGN_5888, 动力蓄电池预留报文
void BSP_ID0x1C1756F4_DATA_CAL(void)
{
}

// BMS 中止充电报文
// BST BMS中止充电 6400 001900H 4 4 10 ms BMS-充电机
void BST_ID0x101956F4_DATA_CAL(void)
{
	DCCanMsg BST_Id0x101956f4;

	uint16_t SOC_Now, SOC_taget;
	uint16_t VoltageCALLNow, VoltageCALLTaget;
	uint32_t VoltageBatterPack, VoltageBatterPackTaget;

	BST_Id0x101956f4.FrameID = 0x101956f4;
	BST_Id0x101956f4.DataLength = sizeof(BST_PGN6400_InfoTypeDef);
	BST_Id0x101956f4.FrameFormat = CAN_FRAME_FORMAT_EFF;
	BST_Id0x101956f4.FrameType = CAN_FRAME_TYPE_DATA;

	get_signals_value(SID_SOC, &SOC_Now, 1);
	get_signals_value(SID_CH_SOC_TAGET, &SOC_taget, 1); // SID_CH_SOC_TAGE

	get_signals_value(SID_VOLTAGE_BATTERY, &VoltageBatterPack, 1);
	get_signals_value(SID_CH_VOLTAGE_TAGET, &VoltageBatterPackTaget, 1); // SID_CH_VOLTAGE_TAGET 目标总电压

	get_signals_value(SID_VOLTAGE_BATTERY, &VoltageCALLNow, 1);
	get_signals_value(SID_CH_CELL_VOLTAGE_TAFGET, &VoltageCALLTaget, 1); // SID_CELL_VOLTAGE_MAX	目标电芯单体电压

	if (SOC_Now >= SOC_taget) // 达到SOC值
	{
		BST_ERROInfo.StopReason.soc_target_reached = 1;
	}

	if (VoltageBatterPack >= VoltageBatterPackTaget) // 总电压达到预设上限
	{
		BST_ERROInfo.StopReason.total_voltage_reached = 1;
	}

	if (VoltageCALLNow >= VoltageCALLTaget) // 单体电压达到预设上限
	{
		BST_ERROInfo.StopReason.cell_voltage_reached = 1;
	}
	if (HldB_CST) // 充电机主动中止
	{
		// HldB_BST = 0;
		BST_ERROInfo.StopReason.charger_terminated = 1;
	}

	uint8_t temp = 0;

	get_signals_value(SID_FCHG_OC_Level_1, &temp, 1); //
	if (temp)
	{
		BST_ERROInfo.ErrorReason.current = 1;
	}

	get_signals_value(SID_CHG_BAT_OV_Level_2, &temp, 1);
	if (temp)
	{
		BST_ERROInfo.ErrorReason.voltage = 1;
	}

	get_signals_value(SID_LEAKAGE_FAIL_Level_2, &temp, 1);
	if (temp)
	{
		BST_ERROInfo.FaultReason.insulation_fault = 0; // 绝缘故障
	}

	get_signals_value(SID_CHG_SOCKET_UOT_Level_2, &temp, 1); // 充电口温度
	if (temp)
	{
		BST_ERROInfo.FaultReason.output_connector_overtemp_fault = 0; // 车辆插座过温故障
	}

	//		get_signals_value(  , &temp, 1); 没传感器
	//		if ( temp )
	//		{
	//		BST_ERROInfo.FaultReason.bms_component_overtemp = 0;          // 车辆内部线束或相关连接器过温
	//		}

	get_signals_value(SID_CHG_CONNECT_FALL, &temp, 1); // 充电连接器故障
	if (temp)

	{
		BST_ERROInfo.FaultReason.charger_connector_fault = 0; // 充电连接器
	}

	get_signals_value(SID_CHG_OT_Level_2, &temp, 1);
	if (temp)
	{
		BST_ERROInfo.FaultReason.battery_overtemp_fault = 0; // 电池组温度过高故障
	}

	get_signals_value(SID_RELAY_FAIL_Level_2, &temp, 1);
	if (temp)
	{
		BST_ERROInfo.FaultReason.high_voltage_relay_fault = 0; // 高压继电器故障
	}

	get_signals_value(SID_LEAKAGE_FAIL_Level_1, &temp, 1);
	if (temp)
	{
		BST_ERROInfo.FaultReason.voltage_CC2_fault = 0; // 检测点2电压检测故障
	}

	//		get_signals_value(SID_LEAKAGE_FAIL_Level_1, &temp, 1);
	//		if ( temp)
	//		{
	//		BST_ERROInfo.FaultReason.other_fault						 =1;          // 其他故障

	//		}
	set_signals_value(SID_CHA_BMS_STOP_REASON, &BST_ERROInfo.StopReason, 1);
	set_signals_value(SID_CHA_BMS_STOP_FAULT_REASON, &BST_ERROInfo.FaultReason, 1);
	set_signals_value(SID_CHA_BMS_STOP_ERROR_REASON, &BST_ERROInfo.ErrorReason, 1);

	memcpy(BST_Id0x101956f4.FrameData, &BST_ERROInfo, BST_Id0x101956f4.DataLength);
	DCCAN_SendMsg(DCCH_CAN, &BST_Id0x101956f4);
}

/*****************************************/
// Receive 500ms
// CCS, PGN_4608, 充电机充电状态报文
void CCS_ID0x1812F456_DATA_CAL(DCCanMsg msg) // 监视充电机当前输出的充电电流、电压值等信息。
{
	CCS_PGN4608_InfoTypeDef *CCS_PGN4608 = (CCS_PGN4608_InfoTypeDef *)msg.FrameData;
	static uint8_t chargePermissionState = 0;

	HldB_CCS = 1;
	HldB_BSM = 1;
	/****不强制***/
	//	HldB_BMV=1;
	//	HldB_BMT=1;
	//	HldB_BSP=1;F

	CCS_TimeoutCounter = 100;					  // BMS 在 1s 内没有收到该报文，即为超时错误，BMS 应立即结束充电
	chm_output_volt = CCS_PGN4608->outputVoltage; //  msg.FrameData[0] + ((msg.FrameData[1] << 8) & 0xff00);	   // 电压输出值（V）
	chm_output_curr = CCS_PGN4608->outputCurrent; // msg.FrameData[2] + ((msg.FrameData[3] << 8) & 0xff00) - 400; // 电流输出值（A）
	set_signals_value(SID_CHG_OCB_CURRENT, &chm_output_curr, 1);
	set_signals_value(SID_CHG_OCB_VOLTAGE, &chm_output_volt, 1);
	chm_total_chrg_time = CCS_PGN4608->accumulatedChargeTime; // msg.FrameData[4] + ((msg.FrameData[5] << 8) & 0xff00);   // 累计充电时间（min）
	// APP_PRINTF("output volt:%d,output curr:%d,DCCH time remaining:%d min\n", chm_output_volt, chm_output_curr, chm_total_chrg_time);

	if (chargePermissionState != CCS_PGN4608->chargePermissionState)
	{
		chargePermissionState = CCS_PGN4608->chargePermissionState;
		APP_PRINTF("[CCS]:DC charge%s;", chargePermissionState ? " pause(-_-)" : " DC Chaegeing (^v^)");
		if (CCS_PGN4608->chargePermissionState == 1)
		{
			CHM_bchrgPem = 1;
		}
		else
		{
			CHM_bchrgPem = 0;
		}
	}

	chargePermissionState = CHM_bchrgPem;
}
// Receive 10ms
// CST, PGN_6656, 充电机中止充电报文
void CST_ID0x101AF456_DATA_CAL(DCCanMsg msg) // 充电机中止充电报文。
{
	ChargeState = STATE_END_CHARGE;

	HldB_CST = 1;
	HldB_BSD = 1;
	HldB_BST = 0;
	HldB_CCS = 0; // CCS 停止发送
	HldB_BCL = 0; // 电池需求报文
	HldB_BCS = 0; //
	HldB_BSM = 0; //

	//	HldB_BMV = 0 ;   //
	//	HldB_BMP = 0 ;   //
	//	HldB_BSP = 0 ;   //
	CST_PGN6656_InfoTypeDef *CST_PGN6656 = (CST_PGN6656_InfoTypeDef *)msg.FrameData;

	chm_stop_factor = CST_PGN6656->StopReason.reasons;
	chm_stop_fault = CST_PGN6656->FaultReason.faults;
	chm_stop_err = CST_PGN6656->ErrorReason.errors;
}

/*---------------------------充电结束阶段-----------------------------*/
// SEND 250ms
// BSD BMS统计数据 7168 001C00H 6 7 250 BMS-充电机
void BSD_ID0x181C56F4_DATA_CAL(void) // BMS 统计数据报文
{
	uint16_t SOC_Now, VoltageCallMax, VoltageCallMin, TempCallMax, TempCallMin;
	DCCanMsg BSD_Id0x181C56f4;
	BSD_PGN7168TypeDef *BSD_PGN7168 = (BSD_PGN7168TypeDef *)BSD_Id0x181C56f4.FrameData;
	BSD_Id0x181C56f4.FrameID = 0x181C56f4;
	BSD_Id0x181C56f4.DataLength = sizeof(BSD_PGN7168TypeDef); // 7;
	BSD_Id0x181C56f4.FrameFormat = CAN_FRAME_FORMAT_EFF;
	BSD_Id0x181C56f4.FrameType = CAN_FRAME_TYPE_DATA;

	get_signals_value(SID_SOC, &SOC_Now, 1);
	BSD_PGN7168->SOC_EndOfCharge = SOC_Now / 10; // SPN3601 中止荷电状态（SOC）VCUSOCDisp

	// 连续数据
	// SID_CELL_VOLTAGE_MAX		电芯最高电压			int16	mV
	// SID_CELL_VOLTAGE_MAX_SN	最高电压电芯序号	int16
	// SID_CELL_VOLTAGE_MIN		电芯最低电压			int16	mV
	// SID_CELL_VOLTAGE_MIN_SN	最低电压电芯序号	int16
	// SID_CELL_TEMPER_MAX			电芯最高温度			int16	℃
	// SID_CELL_TEMPER_MAX_SN	最高温度电芯序号	int16
	// SID_CELL_TEMPER_MIN			电芯最低温度			int16	℃

	get_signals_value(SID_CELL_VOLTAGE_MAX, &VoltageCallMax, 1); // SID_CELL_VOLTAGE_MAX	电芯最高电压	int16
	get_signals_value(SID_CELL_VOLTAGE_MIN, &VoltageCallMin, 1); // SID_CELL_VOLTAGE_MIN	电芯最低电压	int16
	get_signals_value(SID_CELL_TEMPER_MAX, &TempCallMax, 1);	 // SID_CELL_TEMPER_MAX	电芯最高温度
	get_signals_value(SID_CELL_TEMPER_MIN, &TempCallMin, 1);	 // SID_CELL_TEMPER_MIN	电芯最低温度

	BSD_PGN7168->MinSingleCellVoltage = VoltageCallMin;	   // SPN3602 动力蓄电池单体最低电压(V)
	BSD_PGN7168->MaxSingleCellVoltage = VoltageCallMax;	   // SPN3603 动力蓄电池单体最高电压(V)
	BSD_PGN7168->MinBatteryTemperature = TempCallMin + 50; // SPN3604 动力蓄电池最低温度(℃)
	BSD_PGN7168->MaxBatteryTemperature = TempCallMax + 50; // SPN3605 动力蓄电池最高温度(℃)

	if (HldB_CST) // 充电机主动中止
	{
		HldB_BST = 0;
	}
	DCCAN_SendMsg(DCCH_CAN, &BSD_Id0x181C56f4);
}

// Receive 250ms
// CSD, PGN_7424, 充电机统计数据报文
void CSD_ID0x181DF456_DATA_CAL(DCCanMsg msg) // 充电机统计数据报文。
{
	HldB_CST = 0;
	HldB_BSD = 0; // 2023
	CSD_PGN7424_Message_Type *CSD_PGN7424 = (CSD_PGN7424_Message_Type *)msg.FrameData;
	//	chm_acc_chrg_time = msg.FrameData[0] + ((msg.FrameData[1] << 8) & 0xff00);
	//	chm_output_power  = msg.FrameData[2] + ((msg.FrameData[3] << 8) & 0xff00);
	//	chm_mac_no 				= msg.FrameData[4] + ((msg.FrameData[5] << 8) & 0xffffff00) + ((msg.FrameData[6] << 16) & 0xffff0000) + ((msg.FrameData[7] << 24) & 0xff000000);
	chm_acc_chrg_time = CSD_PGN7424->AccumulatedChargingTime;
	chm_output_power = CSD_PGN7424->OutputEnergy_kWh;
	chm_mac_no = CSD_PGN7424->ChargerID;
}

/*---------------------------错误报文-----------------------------*/
// BEM BMS错误报文 7680 001E00H 2 4 250 BMS->充电机
// 当BMS 检测到错误时，发送给充电机充电错误报文，直到 BMS 收到充电机发送的充电机辨识报文(CRM) 或拔掉充电插头为止
// 250mS
void BEM_ID0x081E56F4_DATA_CAL(void)
{
	DCCanMsg BEM_Id0x081E56f4;
	BEM_PGN7680TypeDef *BEM_PGN7680 = (BEM_PGN7680TypeDef *)BEM_Id0x081E56f4.FrameData;
	// uint32_t Temp = 0xFFFFFFFF;
	BEM_Id0x081E56f4.FrameID = 0x081E56f4;
	BEM_Id0x081E56f4.DataLength = 4;
	BEM_Id0x081E56f4.FrameFormat = CAN_FRAME_FORMAT_EFF;
	BEM_Id0x081E56f4.FrameType = CAN_FRAME_TYPE_DATA;

	if (dcch_bSPN2560OvrTim) // 01						//SPN3901 接收 SPN2560=0x00 的充电机辨识报文超时
	{
		BEM_PGN7680->timeoutFlags1.bits.spn3901 = 1;
		APP_PRINTF("\n \n[CEM] rcev: CRM_0,");
	}
	else // 00
	{
		BEM_PGN7680->timeoutFlags1.bits.spn3901 = 0;
	}

	if (dcch_bSPN2560AAOvrTim) // 01					//SPN3902 接收 SPN2560=0xAA 的充电机辨识报文超时
	{
		BEM_PGN7680->timeoutFlags1.bits.spn3902 = 1;
		APP_PRINTF("CRM_AA, ");
	}
	else
	{
		BEM_PGN7680->timeoutFlags1.bits.spn3902 = 0;
	}

	if (dcch_bCMLOvrTim) // 01	SPN3903 接收充电机的时间同步和充电机最大输出能力报文超时
	{
		BEM_PGN7680->timeoutFlags2.bits.spn3903 = 1;
		APP_PRINTF("CML,");
	}
	else // 00
	{
		BEM_PGN7680->timeoutFlags2.bits.spn3903 = 0;
	}
	if (dcch_bSPN2830OvrTim) // 01	SPN3904 接收充电机完成充电准备报文超时
	{
		BEM_PGN7680->timeoutFlags2.bits.spn3904 = 1;
		APP_PRINTF("CRO,");
	}
	else // 00
	{
		BEM_PGN7680->timeoutFlags2.bits.spn3904 = 0;
	}
	if (dcch_bCCSOvrTim) // 01	SPN3905 接受充电机充电状态报文超时
	{
		BEM_PGN7680->timeoutFlags3.bits.spn3905 = 1;
		APP_PRINTF("CCS,");
	}
	else
	{
		BEM_PGN7680->timeoutFlags3.bits.spn3905 = 0;
	}
	if (dcch_bCSTOvrTim) // 01	SPN3906  接收充电机中止充电报文超时
	{
		BEM_PGN7680->timeoutFlags3.bits.spn3906 = 1;
		APP_PRINTF("CST,");
	}
	else // 00
	{
		BEM_PGN7680->timeoutFlags3.bits.spn3906 = 0;
	}
	if (dcch_bCSDOvrTim) // 01	SPN3907  接收充电机充电统计报文超时
	{
		BEM_PGN7680->timeoutFlags4.bits.spn3907 = 0;
		APP_PRINTF("CSD timeout!!\n \n  ");
	}
	else // 00
	{

		BEM_PGN7680->timeoutFlags4.bits.spn3907 = 1;
	}
	DCCAN_SendMsg(DCCH_CAN, &BEM_Id0x081E56f4);
}

// CEM, PGN_7936, 充电机错误报文
// CEM 充电机错误报文 7936 001F00H 2 4 250 充电机-》BMS
void CEM_ID0x081FF456_DATA_CAL(DCCanMsg msg) // 充电机错误报文
{
	HldB_CEM = 1;
	CEM_TimeoutCounter = 100;
	CEM_PGN7936TypeDef *CEM_PGN7936 = (CEM_PGN7936TypeDef *)msg.FrameData;

	if (!CEM_PGN7936)
	{
		return;
	}

	if (1 == CEM_PGN7936->timeoutFlags1.bits.spn3921)
	{
		diw_FCHMRxBMSRcndChrgTiOut = true;
		APP_PRINTF("\r\n [CEM] rcev: BRM,");
	}
	else
	{
		diw_FCHMRxBMSRcndChrgTiOut = false;
	}

	if (1 == CEM_PGN7936->timeoutFlags2.bits.spn3922) // 接收电池充电参数报文超时
	{
		diw_FCHMRxPrmChrgTiOut = true;
		APP_PRINTF("BAT,");
	}
	else
	{
		diw_FCHMRxPrmChrgTiOut = false;
	}

	if (!CEM_PGN7936->timeoutFlags2.bits.spn3923) // 接收 BMS 完 成 充 电 准 备 报 文 超 时
	{
		diw_FCHMRxBMSRdyChrgTiOut = true;
		APP_PRINTF("BRO,");
	}
	else
	{
		diw_FCHMRxBMSRdyChrgTiOut = false;
	}

	if (!CEM_PGN7936->timeoutFlags3.bits.spn3924) //	spn3924 : 2; // 接收电池充电总状态报文超时
	{
		diw_FCHMRxBattStsChrgTiOut = true;
		APP_PRINTF("BCS,");
	}
	else
	{
		diw_FCHMRxBattStsChrgTiOut = false;
	}

	if (!CEM_PGN7936->timeoutFlags3.bits.spn3925) //	spn3925 : 2; // 接收电池充电要求报文超时
	{
		diw_FCHMRxBattReqChrgTiOut = true;
		APP_PRINTF("BCL,");
	}
	else
	{
		diw_FCHMRxBattReqChrgTiOut = false;
	}

	if (!CEM_PGN7936->timeoutFlags3.bits.spn3926) //	spn3926 : 2; // 接收BMS中止充电报文超时
	{
		diw_FCHMRxBMSBreakChrgTiOut = true;
		APP_PRINTF("BST,");
	}
	else
	{
		diw_FCHMRxBMSBreakChrgTiOut = false;
	}

	if (!CEM_PGN7936->timeoutFlags4.bits.spn3927) // spn3927 : 2; // 接收BMS充电统计报文超时
	{
		diw_FCHMRxBMSChrgTiOut = true;
		APP_PRINTF("BSD timeout!!\n \n ");
	}
	else
	{
		diw_FCHMRxBMSChrgTiOut = false;
	}
}

/*---------------------------Analysis-----------------------------*/
void CAN_Analysis(DCCanMsg msg)
{
	switch (msg.FrameID)
	{
	case 0x1826F456:
		CHM_ID0x1826F456_DATA_CAL(msg);
		break; // 充电机握手报文
	case 0x1801F456:
		CRM_ID0x1801F456_DATA_CAL(msg);
		break; // 充电机辨识报文
	case 0x1807F456:
		CTS_ID0x1807F456_DATA_CAL(msg);
		break; // 充电机发送时间同步信息报文
	case 0x1808F456:
		CML_ID0x1808F456_DATA_CAL(msg);
		break; // 充电机最大输出能力报文
	case 0x100AF456:
		CRO_ID0x100AF456_DATA_CAL(msg);
		break; // 充电机输出准备就绪状态报文
	case 0x1812F456:
		CCS_ID0x1812F456_DATA_CAL(msg);
		break; // 充电机充电状态报文
	case 0x101AF456:
		CST_ID0x101AF456_DATA_CAL(msg);
		break; // 充电机中止充电报文
	case 0x181DF456:
		CSD_ID0x181DF456_DATA_CAL(msg);
		break; // 充电机统计数据报文
	case 0x081FF456:
		CEM_ID0x081FF456_DATA_CAL(msg);
		break; // 充电机错误报文
	case 0x1CECF456:
		J1939_Receive_TP_CM(&msg);
		break; // PGN_60416
	case 0x1CEBF456:
		J1939_Receive_TP_DT(&msg); // PGN_60160
		break;
	default:
		break;
	}
}

void Task_OverTime(void)
{
	if (HldB_CCS) // 充电状态
	{
		// 如果 CCS 计数器大于0，则递减计数器；否则，重置保持标志位
		if (CCS_TimeoutCounter > 0) // CCS 计数器大于0，则递减计数器
		{
			CCS_TimeoutCounter--;
		}
		else
		{
			HldB_CCS = 0;
			HldB_BEM = true; // 2023
			HldB_CEM = true; // 2023
		}
	}

	if (HldB_BRM) //
	{
		if (BRM_TimeoutCounter > 0)
		{
			BRM_TimeoutCounter--;
			// 2023
			HldB_CEM = false;
			HldB_CEM = false;
		}
		else
		{
			HldB_BRM = 0;
			// 2023
			HldB_BEM = true;
			HldB_CEM = true;
		}
	}

	if (HldB_CEM == 1) // 充电机错误状态
	{
		if (CEM_TimeoutCounter > 0) // CCS 计数器大于0，则递减计数器
		{
			CEM_TimeoutCounter--;
		}
		else
		{
			HldB_CEM = 0;
			// 重置与充电相关的多个状态标志
			diw_FCHMRxBMSRcndChrgTiOut = false;
			diw_FCHMRxPrmChrgTiOut = false;
			diw_FCHMRxBMSRdyChrgTiOut = false;
			diw_FCHMRxBattStsChrgTiOut = false;
			diw_FCHMRxBattReqChrgTiOut = false;
			diw_FCHMRxBMSBreakChrgTiOut = false;
			diw_FCHMRxBMSChrgTiOut = false;
		}
	}
}

void Task_DCCAN_Ctr_BST(void)
{
	static uint8_t BST_Count = 0;

	if (HldB_BST)
	{
		ChargeState = STATE_END_CHARGE;
		BST_Count++;
		if (BST_Count < 10) // GB2023
		{
			BST_ID0x101956F4_DATA_CAL();
		}
		else
		{
			BST_Count = 0;
			HldB_BST = 0;
		}
		HldB_BCL = 0; // 电池需求报文
		HldB_BCS = 0; //

		// HldB_BCS = 0; //
		HldB_BSM = 0; //
	}
}

// BCL, PGN_4096, 电池充电需求报文
void Task_DCCAN_Ctr_BCL(void)
{
	static boolean_T flag = false;
	static uint8_t counter = 0;
	if (!HldB_BCL)
		return;
	if (flag == false) // fist time
	{
		counter = 5;
	}
	if (counter < 5)
	{
		counter++;
	}
	if (counter == 5)
	{
		BCL_ID0x181056F4_DATA_CAL();
		counter = 0;
	}
	flag = HldB_BCL;
}

// BRM, PGN_512, BMS辨识报文
void Task_DCCAN_Ctr_BRM(void)
{
	static boolean_T flag = false;
	static uint8_t counter = 0;
	if (HldB_BRM != true)
		return;

	if (flag == false) // fist time
	{
		counter = 25;
	}
	if (counter < 25) // next time
	{
		counter++;
	}
	if (counter == 25)
	{
		BRM_ID0x1C0256F4_DATA_CAL();
		counter = 0;
	}
	flag = HldB_BRM;
}

// BRO, PGN_2304, 车辆充电准备就绪状态报文
void Task_DCCAN_Ctr_BRO(void)
{
	static boolean_T flag = false;
	static uint8_t counter = 0;
	if (HldB_BRO != true)
		return;
	if (flag == false)
	{
		counter = 25;
	}
	if (counter < 25)
	{
		counter++;
	}
	if (counter == 25)
	{
		BRO_ID0x100956F4_DATA_CAL();
		counter = 0;
	}
	flag = HldB_BRO;
}

// BCS 电池充电总状态 4352 001100H 7 9 250 ms BMS-充电机
void Task_DCCAN_Ctr_BCS(void)
{
	static boolean_T flag = false;
	static uint8_t counter = 0;
	if (HldB_BCS != true)
		return;
	if (flag == false)
	{
		counter = 25;
	}
	if (HldB_BCS == true && counter < 25)
	{
		counter++;
	}
	if (counter == 25)
	{
		BCS_ID0x1C1156F4_DATA_CAL();
		counter = 0;
	}
	// 收到CST报文(充电机主动中止充电) 或者发送BST报文(BMS主动中止充电) BCS
	flag = HldB_BCS;
}

void Task_DCCAN_Ctr_BSM(void)
{
	static boolean_T flag = false;
	static uint8_t counter = 0;
	if (HldB_BSM != true)
		return;
	if (flag == false)
	{
		counter = 25;
	}
	if (counter < 25)
	{
		counter++;
	}
	if (counter == 25)
	{
		BSM_ID0x181356F4_DATA_CAL();
		counter = 0;
	}
	flag = HldB_BSM;
}

// BSD BMS统计数据 7168 001C00H 6 7 250 BMS-充电机
void Task_DCCAN_Ctr_BSD(void)
{
	static boolean_T flag = false;
	static uint8_t counter = 0;
	if (HldB_BSD != true)
		return;

	if (flag == false)
	{
		counter = 25;
	}
	if (HldB_BSD == true && counter < 25)
	{
		counter++;
	}
	if (counter == 25)
	{
		BSD_ID0x181C56F4_DATA_CAL();
		counter = 0;
	}
	flag = HldB_BSD;
}

// BEM BMS错误报文 7680 001E00H 2 4 250 BMS-充电机
void Task_DCCAN_Ctr_BEM(void)
{
	static boolean_T flag = false;
	static uint8_t counter = 0;
	if (HldB_BEM != true)
		return;
	if (flag == false)
	{
		counter = 25;
	}
	if (counter < 25)
	{
		counter++;
	}
	if (counter == 25)
	{
		BEM_ID0x081E56F4_DATA_CAL();
		counter = 0;
	}
	flag = HldB_BEM;
}

// BHM, PGN_9984, 车辆握手报文
void Task_DCCAN_Ctr_BHM(void)
{
	static boolean_T flag = false; // 当前阶段通过与否
	static uint8_t counter = 0;
	if (HldB_BHM != true)
		return;

	if (flag == false) // fist time enter BHM
	{
		counter = 25;
	}
	if (counter < 25) // 250Ms
	{
		counter++;
	}
	if (counter == 25)
	{
		BHM_ID0x182756F4_DATA_CAL();
		counter = 0;
	}
	flag = HldB_BHM;
}

// BCP, PGN_1536, 车辆充电参数报文
void Task_DCCAN_Ctr_BCP(void)
{
	static boolean_T flag = false;
	static uint8_t counter = 0;
	if (HldB_BCP != true)
		return;
	if (flag == false)
	{
		//		HldB_CRM = 0;
		counter = 50;
	}
	if (HldB_BCP == true && counter < 50)
	{
		counter++;
	}
	if (counter == 50)
	{
		BCP_ID0x1C0656F4_DATA_CAL();
		counter = 0;
	}
	flag = HldB_BCP;
}

boolean_T Task_DCCH_RecvProce(void)
{
	DCCanMsg msg;
	if (CAN_queueOut(&CAN_DCCH_Recv_Queue, &msg) != TRUE)
	{
		return false;
	}
	CAN_Analysis(msg);
	return true;
}

// // 状态监测
// void Task_DCCH_StateMonitor(void)
// {

// }

void Task_CAN_ERRO_pro(void)
{
	// extern int CAN1_Hard_Init(void);
	// RMU_SoftRstSingMod(DCCH_CANx);
	// CAN1_Hard_Init();
}

void Task_DCCH_CANsent_GB(void)
{
	static uint32_t time_pre10ms = 0;
	uint32_t timeNow10ms = 0;
	Task_DCCH_RecvProce();
	J1939_Poll();

	timeNow10ms = get_sys_tick();
	// 处理时间溢出的情况
	if (timeNow10ms < time_pre10ms)
	{
		time_pre10ms = 0; // 重置时间基准
	}
	if (timeNow10ms - time_pre10ms < 10)
	{
		return;
	}
	time_pre10ms = timeNow10ms; // 10mS
	//task_CC2_StateMonitor();
	task_AssistPowermonitor_DCC();
	//Task_Charge_Status();

	Task_OverTime();
	Task_DCCAN_Ctr_BST();
	Task_DCCAN_Ctr_BCL();
	Task_DCCAN_Ctr_BRM();
	Task_DCCAN_Ctr_BRO();
	Task_DCCAN_Ctr_BCS();
	Task_DCCAN_Ctr_BSM();
	Task_DCCAN_Ctr_BSD();
	Task_DCCAN_Ctr_BEM();
	Task_DCCAN_Ctr_BHM();
	Task_DCCAN_Ctr_BCP();
	// BMV,BMT,BSP 为可选报告
}
uint32_t calculate_CAN_ID(CAN_EFF_IDTypeDef CAN_EFF_Info)
{
	uint32_t CAN_ID = 0;
	CAN_ID = CAN_EFF_Info.P << 26 | CAN_EFF_Info.EDP << 25 | CAN_EFF_Info.DP << 24 | CAN_EFF_Info.PF << 16 | CAN_EFF_Info.PS << 8 | CAN_EFF_Info.SA;
	return CAN_ID;
}

/**************************************************************************************** B类系统  ****************************************************************************/

// 充电机版本协商 报文
/*
依次比较以下数据：
ID
	uint8_t CANType;            // 充电机CAN类型 0x00：CAN2.0B；0x01：CANFD； 0x02：CANXL。
	uint8_t VersionResultType;  // 充电机版本协商结果  0x00：继续协商；0x01：协商成功；0x02：协商失败。
	//充电机协商的版本号，本文件规定当前主版本号为2,次版本号和临时版本号为 0,即V2.0.0
	uint8_t ProtocolVersionType[3]; // 协议版本号  主版本号：BYTE1；次版本号：BYTE2；临时版本号：BYTE3
	uint8_t CPVersionType;    // 控制导引版本  0x01：《电动汽车传导充电系统（用于GB/T 20234.3的直流充电系统）》的附录B；0x02：《电动汽车传导充电系统（用于GB/T 20234.3的直流充电系统）》的附录A；
	uint8_t TLVersionType;      // 传输层版本 0x01：GB/T20234.3-2020；0x02：GB/T20234.3-2020-1；0x03：GB/T20234.3-2020-2；0x04：GB/T20234.3-2020-3；0x05：GB/T20234.3-2020-4；0x06：
	uint8_t ReservedType;       // 预留 0


版本协商
7.2.1 总体要求
版本协商是通信协议的引导部分，协商原则、报文定义和信息交互过程固定不变。版本协商过程中，
充电机和车辆通过协商决定本次交互的通信协议版本。版本协商总体要求应符合表3的规定。
表3 版本协商总体要求
序号 项目 要求
1 名称 版本协商
2 目标 充电机和车辆协商决定通信协议版本
3 描述
确认物理连接完成（见《电动汽车传导充电系统（用于GB/T 20234.3的直流充电系统）》），
通信链路建立之后，双方进行通信协议版本协商，向对方发送己方支持的最高协议版本号（版
本的比较即数字的大小比较）并读取对方判断结果，版本协商应满足以下要求：
——如对方发送协商成功且版本无误，或对方发送协商失败，则结束协商。
——如对方发送为继续协商，则判断接收版本是否支持，支持则发送协商成功并将通信版本
修改为协商版本。
——如不支持该版本且低于对方发送的版本，则保持当前版本并“继续协商”。
——如不支持该版本且高于对方版本但有低于对方发送的版本，则根据“期望版本号”调整
协议版本指向低于对方期望版本中最高的版本，发送协议版本并“继续协商”。
——如接收版本已低于最低版本，则发送“协商失败”。
版本协商成功后，车辆持续发送“协商成功”，直到充电机发送下一阶段报文。充电机在接
收车辆发送“协商成功”且版本不低于V2.0.0后进入功能协商阶段。
满足以下任一情况，双方进入附录M通信流程（不需发送阶段确认报文）：
——协商失败；
——从己方首次发送版本协商报文超过15 s；
——车辆收到CHM/CRM报文；
——版本协商成功且协商成功版本低于V2.0.0。
进入附录M通信流程后，充电机应符合《电动汽车传导充电系统（用于GB/T 20234.3的直流
充电系统）》B.3.2的规定，车辆应等待充电机闭合辅源及发送CHM报文。
4 前置条件
确认物理连接完成，通信链路建立后同时开始。
在辅源或报文唤醒车辆后，充电机可根据车辆是否支持版本协商功能重新发起新的版本协商
阶段。
5 协议版本
充电机和车辆宜支持多个版本的通信协议。协商成功时，双方支持相同的协议版本，否则协
商失败。
通信协议版本号由CAN类型、主版本号、次版本号、临时版本号组成。
6
——当前CAN类型为CAN2.0B，同时预留CANFD、CANXL的应用；
——主版本号在通信协议有结构性变化（如功能模块有变化）时更新；
——次版本号在通信协议有较大功能变化时更新；
——临时版本仅用于企业内部的示范、测试等临时用途，正式发布的版本中临时版本号为
0。
6 结束条件
协商成功条件包括：
——版本协商成功，版本低于V2.0.0（如V1.1.0），充电机根据自身需求适时提供低压辅助
电源，双方按照附录M进行信息交互；
——版本协商成功，版本不低于V2.0.0，双方发送“协商成功”，双方按照协商一致的协议
版本进行信息交互。
协商失败条件包括：
——版本协商失败，充电机或车辆发送“协商失败”，双方按附录M进行通信；
——版本协商超时，充电机或车辆发送“协商失败”，双方按附录M进行通信。
退出条件包括：
——在版本协商时，充电机可断开开关S1，双方停止通信，退出充电流程；
——在版本协商时，车辆可断开开关S2，双方停止通信，退出充电流程
*/
void Tsask_DCCAN_Ctr_BCCVD(DCCanMsg RecvMsg)
{
	// 协商次数
	static uint8_t NegotiateCount = 0; // 协商次数
	uint8_t ProtocalVersion = 2;	   // 默认2023为2
	// uint8_t ProtocalType =0;
	// get_signals_value(SID_CAN0_CHG_PROTOCOL_CFG,&ProtocalType,3);//获取协议类型

	// if (RecvMsg.FrameID != SendMsg.FrameID)
	// {
	// 	return;
	// }

	DCCanMsg SendMsg;
	SECC_ChargerVerNeg *SECC_VerNeg = (SECC_ChargerVerNeg *)&RecvMsg.FrameData; // 接收到的充电机数据
	EVCC_ChargerVerNeg *EVCC_VerNeg = (EVCC_ChargerVerNeg *)&SendMsg.FrameData; // 发送给充电机的数据

	// 计算ID
	CAN_EFF_IDTypeDef CAN_EFF_ID = {3, 0, 0, 0x36, 0x56, 0xF4};
	SendMsg.FrameID = calculate_CAN_ID(CAN_EFF_ID);
	SendMsg.DataLength = sizeof(EVCC_ChargerVerNeg);
	SendMsg.FrameFormat = CAN_FRAME_FORMAT_EFF;
	SendMsg.FrameType = CAN_FRAME_TYPE_DATA;

	EVCC_VerNeg->CANType = 0x00;				// 车辆CAN类型 0x00：CAN2.0B；0x01：CANFD； 0x02：CANXL。
	EVCC_VerNeg->VersionResultType = 0x00;		// 车辆版本协商结果  0x00：继续协商；0x01：协商成功；0x02：协商失败。
	EVCC_VerNeg->ProtocolVersionType[0] = 0x02; // 协议版本号 主版本号：BYTE1
	EVCC_VerNeg->ProtocolVersionType[1] = 0x00; // 协议版本号 次版本号：BYTE2
	EVCC_VerNeg->ProtocolVersionType[2] = 0x00; // 协议版本号 临时版本号：BYTE3
	// 控制导引版本 0x01：《电动汽车传导充电系统（用于GB/T 20234.3的直流充电系统）》的附录B；
	// 0x02：《电动汽车传导充电系统（用于GB/T 20234.3的直流充电系统）》的附录A；
	EVCC_VerNeg->CPVersionType = 0x01;
	EVCC_VerNeg->TLVersionType = 0x01; // 传输层版本 0x01：本文件第8章；0xFF：其他；
	EVCC_VerNeg->ReservedType = 0x00;  // 预留 0

	NegotiateCount++;

	if (SECC_VerNeg->VersionResultType == 0x01)
	{
		// 充电机版本协商成功
		EVCC_VerNeg->VersionResultType = 0x01;
	}
	else if (SECC_VerNeg->VersionResultType == 0x02)
	{
		// 充电机版本协商失败
		EVCC_VerNeg->VersionResultType = 0x02;
		NegotiateCount = 0;
		return;
		// 结束
	}
	else
	{
		// 继续协商
		if (SECC_VerNeg->ProtocolVersionType[0] > EVCC_VerNeg->ProtocolVersionType[0])
		{
			EVCC_VerNeg->VersionResultType = 0x00; // 继续协商
		}
		else if (SECC_VerNeg->ProtocolVersionType[0] < EVCC_VerNeg->ProtocolVersionType[0])
		{
			EVCC_VerNeg->VersionResultType = 0x00; // 继续协商
		}
		else
		{
			EVCC_VerNeg->VersionResultType = 0x01; // 协商成功
		}
	}

	// 发送协商结果
	DCCAN_SendMsg(DCCH_CAN, &SendMsg);
}

// 车辆版本协商 报文 发送

void DCCAN_Ctr_EVCC(uint8_t VersionResultType, uint8_t ProtocolVersionType1, uint8_t ProtocolVersionType2)
{
	DCCanMsg msg;
	EVCC_ChargerVerNeg evccVerNeg;
	CAN_EFF_IDTypeDef CAN_EFF_ID = {3, 0, 0, 0x36, 0x56, 0xF4};

	// 计算ID
	msg.FrameID = calculate_CAN_ID(CAN_EFF_ID);
	msg.DataLength = sizeof(EVCC_ChargerVerNeg);

	evccVerNeg.CANType = 0x00;				  // 车辆CAN类型 0x00：CAN2.0B；0x01：CANFD； 0x02：CANXL。
	evccVerNeg.VersionResultType = 0x01;	  // 车辆版本协商结果  0x00：继续协商；0x01：协商成功；0x02：协商失败。
	evccVerNeg.ProtocolVersionType[0] = 0x01; // 协议版本号 主版本号：BYTE1
	evccVerNeg.ProtocolVersionType[1] = 0x00; // 协议版本号 次版本号：BYTE2
	evccVerNeg.ProtocolVersionType[2] = 0x00; // 协议版本号 临时版本号：BYTE3
											  // 控制导引版本 0x01：《电动汽车传导充电系统（用于GB/T 20234.3的直流充电系统）》的附录B；
											  // 0x02：《电动汽车传导充电系统（用于GB/T 20234.3的直流充电系统）》的附录A；
	evccVerNeg.CPVersionType = 0x01;
	evccVerNeg.TLVersionType = 0x01; // 传输层版本 0x01：本文件第8章；0xFF：其他；
	evccVerNeg.ReservedType = 0x00;	 // 预留 0
}
