//#include "type.h"
//#include "board_config.h"
//#include "led_ctrl.h"
#include "hal_os.h"
#include "battery_dev.h"
#include "board.h"
#include "SCM63X.h"
#ifdef PROGRAM_TYPE_APP
	#define TIME_1000MS  1000*1
	#define TIME_300MS  300
	#define TIME_50MS  50
#endif
 
void led_runing_flip(void)
{
	static char flag =0;
	if(flag==0)
	{
		flag=1;
		LED1_HIGH;
	}
	else
	{
		flag=0;
		LED1_LOW;
	} 
}



static void led_ctrl_task(void)
{
    unsigned int tick;
    static unsigned int tick_save;
	static unsigned short flag_model = 1000;
	
	unsigned char mos_status[2];
	(void)get_signals_value(SID_CHG_RELAY_STATUS, mos_status,2);

	uint16_t power_status = 0;
	(void)get_signals_value(SID_SLA_RT_POWER_STATUS, &power_status,1);

	if(power_status == 0)
	{
		LED1_LOW;
		return;
	}
 
	if(mos_status[0]==0)
	{
		flag_model = TIME_50MS;
	}
	else if(mos_status[1]==0)
	{	
		flag_model = TIME_300MS;
	}
	else
	{
		flag_model = TIME_1000MS;
	}
	
	if(mos_status[0]==0  &&  mos_status[1]==0)
	{
		flag_model = TIME_1000MS*2;
	}
	
    tick = get_sys_tick();
	if(tick-tick_save <flag_model)
	{
		return;
	}
	tick_save = get_sys_tick();

	led_runing_flip();
	
}

APP_TASK_DEFINE(led_ctrl_task,"1");


