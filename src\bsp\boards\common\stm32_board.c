#include "stm32f1xx.h"
#include "board_config.h"
#include "board.h"
#include "hal_os.h"
#include "type.h"
#include <stdio.h>


void mcu_do_reset(void)
{
    NVIC_SystemReset();
}

//================================================
unsigned int irq_lock(void)
{
    unsigned int key;
    key = __get_PRIMASK();
    __disable_irq();
    return key;
}
void irq_unlock(unsigned int key)
{
    __set_PRIMASK(key);
}

//=================================================
unsigned int get_sys_tick(void)
{
    unsigned int key = irq_lock();
    unsigned int ticks = HAL_GetTick();
    irq_unlock(key);
    return ticks;
}

void sys_time_delay(unsigned int ticks)
{
    HAL_Delay(ticks);
}

void delay_us(unsigned int time_us)
{
    unsigned int last = SysTick->VAL;
    unsigned int delta = time_us*SysTick->LOAD/(1000/uwTickFreq);
	/* Wait until the given number of time have passed */
	while ((last - SysTick->VAL) < delta) {
        ;
	}
}

#if defined(SYS_TICK_WDT_ENABLE)&&defined(PROGRAM_TYPE_APP)
#if !defined(SYS_TICK_WDT_TIME)
#define SYS_TICK_WDT_TIME       10000
#endif
static unsigned int s_sys_tick_wdt_count = 0;
static void sys_tick_wdt_feed(void)
{
    s_sys_tick_wdt_count = 0;
}
APP_TASK_DEFINE(sys_tick_wdt_feed,"99");
#endif
//systemtick interrupt handler. override  the function in startup_xx.s
void SysTick_Handler(void)
{
    HAL_IncTick();
    #if defined(SYS_TICK_WDT_ENABLE)&&defined (PROGRAM_TYPE_APP)
    if (s_sys_tick_wdt_count++ > SYS_TICK_WDT_TIME)
    {
        reset_board(SWDT_TIMEOUT_RESET);
    }
    #endif
}

void NMI_Handler(void)
{
    APP_PRINTF("NMI INT accur!\r\n");
    NVIC_SystemReset();
}
void HardFault_Handler(void)
{
    APP_PRINTF("HardFault INT accur!\r\n");
    NVIC_SystemReset();
}
void BusFault_Handler(void)
{
    APP_PRINTF("BusFault INT accur!\r\n");
    NVIC_SystemReset();
}
void UsageFault_Handler(void)
{
    APP_PRINTF("UsageFault INT accur!\r\n");
    NVIC_SystemReset();
}

void Default_Handler(void)
{
    APP_PRINTF("Unexpected INT accur!\r\n");
    NVIC_SystemReset();
}

