// #include "type.h"
// #include "board_config.h"
// #include "led_ctrl.h"
#include "hal_os.h"
#include "battery_dev.h"
#include "board.h"
#include "SCM63X.h"
#ifdef PROGRAM_TYPE_APP
#define TIME_1000MS 500 * 1
#define TIME_300MS 300
#define TIME_50MS 50
#endif

void led_runing_flip(void)
{
	static char flag = 0;
	if (flag == 0)
	{
		flag = 1;
		LED1_HIGH();
	}
	else
	{
		flag = 0;
		LED1_LOW();
	}
}

extern unsigned int tick_WD;

extern unsigned char flag_powerdown_ctrl;
static void led_ctrl_task(void)
{
	tick_WD = 0;

	unsigned int tick;
	static unsigned int tick_save;
	static unsigned short flag_model = 1000;

	unsigned short rt_key = 0;
	static int led_tick_save = 0;
	get_signals_value(SID_SLA_RT_KEY_PIN, &rt_key, 1);
	unsigned short key_type = 0;
	get_signals_value(SID_POWER_ON_TYPE, &key_type, 1);
	if (flag_powerdown_ctrl == 1)
	{
		LED1_LOW();
		return;
	}
	else if (rt_key && (key_type != 1))
	{
		tick_save = get_sys_tick();
		LED1_HIGH();
		return;
	}

	unsigned char mos_status[2];
	(void)get_signals_value(SID_MAJOR_ALM_AMOUNTS, mos_status, 2);

	if (mos_status[1] == 1)
	{
		flag_model = TIME_50MS;
	}
	else if (mos_status[0] == 1)
	{
		flag_model = TIME_300MS;
	}
	else
	{
		flag_model = TIME_1000MS;
	}

	if (mos_status[0] == 0 && mos_status[1] == 0)
	{
		flag_model = TIME_1000MS * 2;
	}

	tick = get_sys_tick();
	if (tick - tick_save < flag_model)
	{
		return;
	}
	tick_save = get_sys_tick();

	led_runing_flip();
}

APP_TASK_DEFINE(led_ctrl_task, "1");
