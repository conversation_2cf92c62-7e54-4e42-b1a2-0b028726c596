VERSION "2.0"


NS_ : 
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_
	BO_TX_BU_
	BA_DEF_REL_
	BA_REL_
	BA_DEF_DEF_REL_
	BU_SG_REL_
	BU_EV_REL_
	BU_BO_REL_
	SG_MUL_VAL_

BS_:

BU_: BMS VCU


BO_ 752 BMS_to_VCU: 8 BMS
 SG_ Ubus_Voltage : 0|16@1+ (0.1,0) [0|10000] "V"  VCU
 SG_ Ibus_Current : 16|16@1+ (0.1,-3200) [0|65535] "A"  VCU
 SG_ SOC : 32|8@1+ (0.4,0) [0|100] "%"  VCU
 SG_ Capacity : 40|8@1+ (5,0) [0|1250] "Ah"  VCU
 SG_ Flag : 48|8@1+ (1,0) [0|255] ""  VCU
 SG_ Reserved : 56|8@1+ (1,0) [0|255] ""  VCU

BO_ 880 VCU_to_BMS: 8 VCU
 SG_ Reserved1 : 0|8@1+ (1,0) [0|255] ""  BMS
 SG_ Reserved2 : 8|8@1+ (1,0) [0|255] ""  BMS
 SG_ Reserved3 : 16|8@1+ (1,0) [0|255] ""  BMS
 SG_ Reserved4 : 24|8@1+ (1,0) [0|255] ""  BMS
 SG_ Reserved5 : 32|8@1+ (1,0) [0|255] ""  BMS
 SG_ Reserved6 : 40|8@1+ (1,0) [0|255] ""  BMS
 SG_ Reserved7 : 48|8@1+ (1,0) [0|255] ""  BMS
 SG_ Reserved8 : 56|8@1+ (1,0) [0|255] ""  BMS




