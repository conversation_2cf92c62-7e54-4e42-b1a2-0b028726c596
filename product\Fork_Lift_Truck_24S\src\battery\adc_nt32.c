
#include "adc_nt32.h"
 

 

volatile u16 s_adc_result[ADC2_DATA_NUMB];
/*
NTC
4 T_MOS  PB1   ADC2_IN3
*/
void adc_init(u8 flag)
{
		ADC_InitType   ADC_InitStructure;
		GPIO_InitType GPIO_InitStructure;
		DMA_InitType DMA_InitStructure;
		ADC_DeInit(ADC2);
	
		RCC_EnableAHBPeriphClk(RCC_AHB_PERIPH_DMA1 | RCC_AHB_PERIPH_DMA2, ENABLE);
		RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOB, ENABLE);
		RCC_EnableAHBPeriphClk(RCC_AHB_PERIPH_ADC2 ,ENABLE);

		GPIO_InitStructure.Pin = GPIO_PIN_1;
		GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AIN;
		GPIO_InitStructure.GPIO_Speed = GPIO_INPUT;
		GPIO_InitPeripheral(GPIOB, &GPIO_InitStructure);
 
		GPIO_InitStructure.Pin = GPIO_PIN_14;
		GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
		GPIO_InitStructure.GPIO_Speed = GPIO_Speed_10MHz;
		GPIO_InitPeripheral(GPIOB, &GPIO_InitStructure);
		GPIO_ResetBits(GPIOB,GPIO_PIN_14);
	
		/*  --------------------------------------------------------------------------------------------*/
		/* DMA1 channel2 ADC2   */
//		DMA_InitStructure.PeriphAddr     = (uint32_t)&ADC2->DAT;
//		DMA_InitStructure.MemAddr        = (uint32_t)&s_adc_result;
//		DMA_InitStructure.Direction      = DMA_DIR_PERIPH_SRC;
//		DMA_InitStructure.BufSize        = ADC2_DATA_NUMB;
//		DMA_InitStructure.PeriphInc      = DMA_PERIPH_INC_DISABLE;
//		DMA_InitStructure.DMA_MemoryInc  = DMA_MEM_INC_ENABLE;
//		DMA_InitStructure.PeriphDataSize = DMA_PERIPH_DATA_SIZE_HALFWORD;
//		DMA_InitStructure.MemDataSize    = DMA_MemoryDataSize_HalfWord;
//		DMA_InitStructure.CircularMode   = DMA_MODE_CIRCULAR;
//		DMA_InitStructure.Priority       = DMA_PRIORITY_HIGH;
//		DMA_InitStructure.Mem2Mem        = DMA_M2M_DISABLE;
//		DMA_Init(DMA1_CH8, &DMA_InitStructure);
//		/* Enable DMA1 channel1 */
//		DMA_EnableChannel(DMA1_CH8, ENABLE);

		ADC_InitStructure.WorkMode       = ADC_WORKMODE_INDEPENDENT;
		ADC_InitStructure.MultiChEn      = ENABLE;
		ADC_InitStructure.ContinueConvEn = ENABLE;
		ADC_InitStructure.ExtTrigSelect  = ADC_EXT_TRIGCONV_NONE;
		ADC_InitStructure.DatAlign       = ADC_DAT_ALIGN_R;
		ADC_InitStructure.ChsNumber      = 1;
		ADC_Init(ADC2, &ADC_InitStructure);		
		
		/* ADC2 regular channel14 configuration */
		ADC_ConfigRegularChannel(ADC2, ADC2_Channel_03_PB1, 1, ADC_SAMP_TIME_41CYCLES5);

		ADC_EnableDMA(ADC2, ENABLE);
		ADC_Enable(ADC2, ENABLE);

		while(ADC_GetFlagStatusNew(ADC2,ADC_FLAG_RDY) == RESET)
			;

		ADC_StartCalibration(ADC2);

		while (ADC_GetCalibrationStatus(ADC2))
			;

		ADC_EnableSoftwareStartConv(ADC2, ENABLE);
}








