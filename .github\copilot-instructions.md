# BMS Battery Management System - AI Coding Instructions

This is an embedded C firmware codebase for Battery Management Systems (BMS) targeting forklift trucks and industrial vehicles. Understanding the signal processing architecture and product variants is essential for effective development.

## Architecture Overview

### Multi-Product Structure
- **Product Variants**: `Fork_Lift_Truck_8S`, `Fork_Lift_Truck_18S`, `Fork_Lift_Truck_24S`, `Fork_Lift_Truck_36S`, `Fork_Lift_Truck_8S_MOS`, `GD32_BMS_16S`
- Each product has its own `src/battery/battery_dev.h` with SID (Signal ID) definitions specific to cell count and hardware
- **Build Pattern**: Each product has `build.bat` using Keil µVision: `UV4.exe -b keil_project\project\{boot|app}.uvprojx`

### Dual-Boot Architecture
- **Boot + App Split**: `src/boot.c` and `src/app.c` with flash address management
- Boot-to-app jumping logic in `app.c` main() function
- Flash layout controlled by `BOOT_ADDR_FLASH_BEGIN` and `APP_ADDR_FLASH_BEGIN` macros

### Core Signal Processing System
The heart of this BMS is a sophisticated signal processing system using SID (Signal ID) constants:

#### Signal ID Ranges & Types
```c
#define RT_SIG_ID_MAX       0x1000  // Real-time signals
#define CTRL_SIG_ID_MAX     0x2000  // Control signals  
#define SETTING_SIG_ID_MAX  0x3000  // Configuration settings
#define ALARM_SIG_ID_MAX    0x4000  // Alarm signals
#define STATICS_SIG_ID_MAX  0x5000  // Statistics
#define TABLES_SIG_ID_MAX   0x6000  // Lookup tables
```

#### Universal Signal API
All signal access uses these functions:
- `int get_signals_value(unsigned short start_id, void *value, int count)`
- `int set_signals_value(unsigned short start_id, void *value, int count)`

Example usage from `product.c`:
```c
(void)get_signals_value(SID_PACK_TEMPER_AMOUNT, &temp, 1);
(void)set_signals_value(SID_TEMPERATURE_AMOUNT, &temp, 1);
```

## Key Components

### Battery Device Layer (`src/modules/core/proc_signal.inc`)
- **Signal Tables**: RT (real-time), CTRL (control), SETTING, ALM (alarm), STATICS using binary search
- **Data Types**: `SIG_TYPE_UINT8/16/32`, `SIG_TYPE_INT8/16/32`, `SIG_TYPE_STRING8/16/32/64`, `SIG_TYPE_TABLE512/1024`
- **Endianness Handling**: `g_sig_BL_proc_mode` flag controls big/little endian processing

### Hardware Abstraction (`src/bsp/boards/`)
- Board-specific implementations per product variant
- Driver layer: `src/bsp/driver/` contains hardware drivers
- CMSIS integration: `src/bsp/cmsis/`

### Communication Protocols (`src/modules/host_comm/`)
- **Vehicle Protocols**: CAN-based communication with host vehicles
- **Charger Protocols**: Multiple charger manufacturer protocols (see `doc/ChargerProtocol/`)
- **Cloud Communication**: 4G/cellular connectivity support

### Battery-Specific Modules (`src/modules/`)
- **AFE (Analog Front End)**: `afe/` - battery cell monitoring
- **Balancing**: `balance.c` - cell balancing logic
- **SOX Calculations**: `battery/sox.c` - State of Charge/Health
- **Thermal Management**: `heater_ctrl.c` - heating system control
- **Protection Logic**: `control_logic.c` - safety and protection algorithms

## Development Patterns

### Product Configuration
When working on specific products:
1. Navigate to `product/{ProductName}/src/` for product-specific code
2. Check `battery/battery_dev.h` for SID definitions
3. Examine `sig_value_def.h` for signal value ranges and defaults

### Signal Processing Workflow
1. **Define SIDs** in product's `battery_dev.h`: `#define SID_NEW_SIGNAL 0x00XX`
2. **Add to tables** in `battery_dev.c`: Add entry to appropriate signal configuration array
3. **Use API calls**: Access via `get_signals_value()`/`set_signals_value()`

### Build Process
- **Clean Build**: Run `keil_clean.bat` to remove build artifacts
- **Per-Product**: Execute `product/{ProductName}/build.bat`  
- **Manual Build**: Use Keil µVision with project files in `keil_project/project/`

### Module Integration Pattern
Modules use `.inc` files for shared implementation:
- `src/modules/core/proc_signal.inc` - Signal processing core
- `src/modules/battery/ctrl_signal_proc.inc` - Control signal handlers
- `src/modules/battery/alarm_judge.inc` - Alarm judgment logic

### Task System
- **RT-Thread Integration**: Uses RT-Thread RTOS components
- **Task Definition**: `APP_TASK_DEFINE(task_name, "priority")`
- **Periodic Tasks**: Signal processing, statistics, settings storage

## Communication Architecture

### CAN Protocol Framework (`src/modules/vehicle_protocol/`)
- **Frame Format**: Standard/Extended CAN frames
- **Baud Rates**: Configurable from 1M to 20k bps  
- **Multi-Protocol**: Support for various vehicle manufacturer protocols

### Protocol Documentation
Extensive protocol documentation in `doc/ChargerProtocol/` includes:
- DBC files for CAN message definitions
- Manufacturer-specific charging protocols
- Communication timing requirements

## Critical Development Notes

### Endianness Awareness
The signal system handles both big-endian and little-endian data through `g_sig_BL_proc_mode`. Always check endianness requirements when adding new signals.

### Flash Storage Management
- **Settings Persistence**: Automatic storage with change detection
- **Statistics Storage**: Periodic backup of operational statistics  
- **Table Storage**: Large lookup tables stored in dedicated flash sections

### Product Variant Considerations
When modifying core modules, verify compatibility across all product variants. Each product may have different:
- Cell counts (8S, 16S, 18S, 24S, 36S)
- Hardware configurations (MOS vs standard)
- Signal ID ranges and definitions

### Real-time Constraints
This is real-time embedded firmware. Consider:
- Interrupt context limitations
- Task priority and timing
- Memory constraints
- Power management requirements
